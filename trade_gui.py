from futu import *
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import webbrowser
import re
import threading
import http.server
import socketserver
import json
import urllib.parse

# 添加衍生品映射功能
try:
    from core.derivative_mapper import DerivativeMapper
    from core.smart_price_mapper import SmartPriceMapper
    DERIVATIVE_MAPPING_AVAILABLE = True
    SMART_MAPPING_AVAILABLE = True
except ImportError:
    DERIVATIVE_MAPPING_AVAILABLE = False
    SMART_MAPPING_AVAILABLE = False
    print("衍生品映射功能不可用，請檢查core模塊")

class TradeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("富途交易助手")
        self.root.geometry("1200x800")
        
        # 註冊窗口關閉事件處理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 創建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左側交易區域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 模式選擇區域
        mode_frame = ttk.LabelFrame(left_frame, text="交易模式", padding="5")
        mode_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 模式選擇變量
        self.mode_var = tk.StringVar(value="simulate")
        
        # 模式選擇單選按鈕
        ttk.Radiobutton(mode_frame, text="模擬交易", 
                       variable=self.mode_var, value="simulate",
                       command=self.on_mode_change).pack(side=tk.LEFT, padx=20)
        ttk.Radiobutton(mode_frame, text="實盤交易", 
                       variable=self.mode_var, value="real",
                       command=self.on_mode_change).pack(side=tk.LEFT, padx=20)
        
        # 股票代碼輸入
        code_frame = ttk.Frame(left_frame)
        code_frame.pack(fill=tk.X, pady=5)
        ttk.Label(code_frame, text="股票代碼:").pack(side=tk.LEFT)
        self.code_var = tk.StringVar(value="HK.00700")
        ttk.Entry(code_frame, textvariable=self.code_var, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(code_frame, text="獲取報價", 
                  command=self.get_quote).pack(side=tk.LEFT, padx=5)
        
        # 價格和數量輸入區域
        input_frame = ttk.Frame(left_frame)
        input_frame.pack(fill=tk.X, pady=5)
        
        # 價格輸入
        price_frame = ttk.Frame(input_frame)
        price_frame.pack(side=tk.LEFT, padx=20)
        ttk.Label(price_frame, text="價格:").pack(side=tk.LEFT)
        self.price_var = tk.StringVar()
        ttk.Entry(price_frame, textvariable=self.price_var, 
                 width=10).pack(side=tk.LEFT, padx=5)
        
        # 數量輸入
        qty_frame = ttk.Frame(input_frame)
        qty_frame.pack(side=tk.LEFT, padx=20)
        ttk.Label(qty_frame, text="數量:").pack(side=tk.LEFT)
        self.qty_var = tk.StringVar(value="100")
        ttk.Entry(qty_frame, textvariable=self.qty_var,
                 width=10).pack(side=tk.LEFT, padx=5)

        # SL/TP輸入區域
        sl_tp_frame = ttk.Frame(left_frame)
        sl_tp_frame.pack(fill=tk.X, pady=5)

        # 止損輸入
        sl_frame = ttk.Frame(sl_tp_frame)
        sl_frame.pack(side=tk.LEFT, padx=20)
        ttk.Label(sl_frame, text="止損價:").pack(side=tk.LEFT)
        self.sl_var = tk.StringVar(value="")
        self.sl_entry = ttk.Entry(sl_frame, textvariable=self.sl_var, width=10)
        self.sl_entry.pack(side=tk.LEFT, padx=5)

        # 止盈輸入
        tp_frame = ttk.Frame(sl_tp_frame)
        tp_frame.pack(side=tk.LEFT, padx=20)
        ttk.Label(tp_frame, text="止盈價:").pack(side=tk.LEFT)
        self.tp_var = tk.StringVar(value="")
        self.tp_entry = ttk.Entry(tp_frame, textvariable=self.tp_var, width=10)
        self.tp_entry.pack(side=tk.LEFT, padx=5)

        # SL/TP狀態指示
        self.sl_tp_status_var = tk.StringVar(value="")
        ttk.Label(sl_tp_frame, textvariable=self.sl_tp_status_var,
                 foreground="blue").pack(side=tk.RIGHT, padx=10)
        
        # 模擬交易按鈕
        sim_frame = ttk.LabelFrame(left_frame, text="模擬交易", padding="5")
        sim_frame.pack(fill=tk.X, pady=5)
        ttk.Button(sim_frame, text="模擬買入", 
                  command=lambda: self.place_order("BUY", True),
                  style="Buy.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(sim_frame, text="模擬賣出", 
                  command=lambda: self.place_order("SELL", True),
                  style="Sell.TButton").pack(side=tk.LEFT, padx=5)
        
        # 實盤交易按鈕
        real_frame = ttk.LabelFrame(left_frame, text="實盤交易", padding="5")
        real_frame.pack(fill=tk.X, pady=5)
        ttk.Button(real_frame, text="實盤買入", 
                  command=lambda: self.place_order("BUY", False),
                  style="RealBuy.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(real_frame, text="實盤賣出", 
                  command=lambda: self.place_order("SELL", False),
                  style="RealSell.TButton").pack(side=tk.LEFT, padx=5)
        
        # 保存框架為實例變量
        self.sim_frame = sim_frame
        self.real_frame = real_frame
        
        # 外部API設置區域
        api_frame = ttk.LabelFrame(left_frame, text="外部API設置", padding="5")
        api_frame.pack(fill=tk.X, pady=5)
        
        # 勾選框
        self.api_enabled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(api_frame, text="啟用接收外部下單API", 
                       variable=self.api_enabled_var,
                       command=self.toggle_api_server).pack(anchor=tk.W, pady=5)
        
        # 外部API商品輸入框
        api_input_frame = ttk.Frame(api_frame)
        api_input_frame.pack(fill=tk.X, pady=5)
        ttk.Label(api_input_frame, text="外部API商品:").pack(side=tk.LEFT)
        self.api_symbol_var = tk.StringVar(value="hk50.cash")
        ttk.Entry(api_input_frame, textvariable=self.api_symbol_var, width=15).pack(side=tk.LEFT, padx=5)

        # 衍生品映射設置
        if DERIVATIVE_MAPPING_AVAILABLE:
            derivative_frame = ttk.Frame(api_frame)
            derivative_frame.pack(fill=tk.X, pady=5)

            self.use_derivative_mapping_var = tk.BooleanVar(value=False)
            ttk.Checkbutton(derivative_frame, text="啟用衍生品價格映射",
                           variable=self.use_derivative_mapping_var).pack(side=tk.LEFT)

            ttk.Label(derivative_frame, text="映射ID:").pack(side=tk.LEFT, padx=(20, 5))
            self.derivative_mapping_id_var = tk.StringVar(value="nikkei_call")
            mapping_combo = ttk.Combobox(derivative_frame, textvariable=self.derivative_mapping_id_var,
                                       width=15, state="readonly")
            mapping_combo.pack(side=tk.LEFT, padx=5)

            # 加載映射選項
            try:
                mappings = self.derivative_mapper.get_all_mappings()
                mapping_names = [f"{mid} ({mapping.get('name', '')})" for mid, mapping in mappings.items()]
                mapping_combo['values'] = mapping_names
                if mapping_names:
                    mapping_combo.current(0)
            except:
                pass

        # 智能SL/TP映射設置
        if SMART_MAPPING_AVAILABLE:
            smart_frame = ttk.LabelFrame(self.root, text="智能SL/TP映射", padding="10")
            smart_frame.pack(fill=tk.X, padx=10, pady=5)

            # 第一行：啟用智能映射
            smart_row1 = ttk.Frame(smart_frame)
            smart_row1.pack(fill=tk.X, pady=5)

            self.use_smart_mapping_var = tk.BooleanVar(value=False)
            ttk.Checkbutton(smart_row1, text="啟用智能SL/TP映射",
                           variable=self.use_smart_mapping_var,
                           command=self.toggle_smart_mapping).pack(side=tk.LEFT)

            # 第二行：認購證設置
            smart_row2 = ttk.Frame(smart_frame)
            smart_row2.pack(fill=tk.X, pady=5)

            ttk.Label(smart_row2, text="認購證代碼:").pack(side=tk.LEFT)
            self.warrant_code_var = tk.StringVar(value="HK.28123")
            ttk.Entry(smart_row2, textvariable=self.warrant_code_var, width=12).pack(side=tk.LEFT, padx=5)

            ttk.Label(smart_row2, text="認購證名稱:").pack(side=tk.LEFT, padx=(20, 5))
            self.warrant_name_var = tk.StringVar(value="日經認購證")
            ttk.Entry(smart_row2, textvariable=self.warrant_name_var, width=15).pack(side=tk.LEFT, padx=5)

            # 第三行：操作按鈕
            smart_row3 = ttk.Frame(smart_frame)
            smart_row3.pack(fill=tk.X, pady=5)

            ttk.Button(smart_row3, text="設置映射", command=self.setup_smart_mapping).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(smart_row3, text="獲取快照", command=self.get_market_snapshots).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(smart_row3, text="開始監控", command=self.start_price_monitoring).pack(side=tk.LEFT, padx=(0, 10))

            # 狀態顯示
            self.smart_status_var = tk.StringVar(value="智能映射未啟用")
            ttk.Label(smart_row3, textvariable=self.smart_status_var).pack(side=tk.RIGHT)
        
        # API服务器状态标签
        self.api_status_var = tk.StringVar(value="API服務器: 未啟動")
        ttk.Label(api_frame, textvariable=self.api_status_var).pack(anchor=tk.W, pady=5)
        
        # 初始化API服務器相關變量
        self.api_server = None
        self.api_thread = None
        self.api_port = 8888  # 默認端口

        # 初始化衍生品映射器
        if DERIVATIVE_MAPPING_AVAILABLE:
            self.derivative_mapper = DerivativeMapper()
        else:
            self.derivative_mapper = None

        # 初始化智能價格映射器
        if SMART_MAPPING_AVAILABLE:
            self.smart_mapper = SmartPriceMapper(self.quote_ctx)
            self.smart_mapper.set_sl_tp_callback(self.update_sl_tp_from_mapping)
        else:
            self.smart_mapper = None
        
        # 右側信息顯示區域
        self.right_frame = ttk.Frame(main_frame)
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 創建模擬帳戶和實盤帳戶框架
        self.create_account_frames()
        
        # 設置按鈕顏色
        style = ttk.Style()
        style.configure("Buy.TButton", foreground="red")
        style.configure("Sell.TButton", foreground="green")
        style.configure("RealBuy.TButton", foreground="red", font=('Helvetica', 9, 'bold'))
        style.configure("RealSell.TButton", foreground="green", font=('Helvetica', 9, 'bold'))
        
        # 狀態欄
        self.status_var = tk.StringVar()
        ttk.Label(root, textvariable=self.status_var).pack(side=tk.BOTTOM, fill=tk.X)
        
        # 初始化帳戶狀態
        self.has_simulate_acc = False
        self.has_real_acc = False
        
        # 初始化連接
        self.trade_ctx = None
        self.quote_ctx = None
        self.connect()
        
        # 檢查帳戶狀態
        self.check_account_status()
        
        # 初始化持倉數據
        self.refresh_position(True)
        self.refresh_position(False)
        self.refresh_orders(True)
        self.refresh_orders(False)
        
        # 初始顯示模擬帳戶
        self.on_mode_change()
        
    def create_account_frames(self):
        """創建帳戶框架"""
        # 模擬帳戶區域
        self.sim_account_frame = ttk.LabelFrame(self.right_frame, text="模擬帳戶", padding="10")
        
        # 模擬帳戶持倉
        sim_position_frame = ttk.LabelFrame(self.sim_account_frame, text="持倉", padding="5")
        sim_position_frame.pack(fill=tk.BOTH, expand=True)
        self.sim_position_tree = self.create_position_tree(sim_position_frame)
        ttk.Button(sim_position_frame, text="刷新持倉", 
                  command=lambda: self.refresh_position(True)).pack(pady=5)
        
        # 模擬帳戶掛單
        sim_order_frame = ttk.LabelFrame(self.sim_account_frame, text="掛單狀態", padding="5")
        sim_order_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.sim_order_tree = self.create_order_tree(sim_order_frame)
        
        # 創建按鈕框架
        sim_order_button_frame = ttk.Frame(sim_order_frame)
        sim_order_button_frame.pack(pady=5)
        ttk.Button(sim_order_button_frame, text="刷新掛單", 
                  command=lambda: self.refresh_orders(True)).pack(side=tk.LEFT, padx=5)
        ttk.Button(sim_order_button_frame, text="撤銷所有訂單",
                  command=lambda: self.cancel_all_orders(True)).pack(side=tk.LEFT, padx=5)
        
        # 實盤帳戶區域
        self.real_account_frame = ttk.LabelFrame(self.right_frame, text="實盤帳戶", padding="10")
        
        # 實盤帳戶持倉
        real_position_frame = ttk.LabelFrame(self.real_account_frame, text="持倉", padding="5")
        real_position_frame.pack(fill=tk.BOTH, expand=True)
        self.real_position_tree = self.create_position_tree(real_position_frame)
        ttk.Button(real_position_frame, text="刷新持倉", 
                  command=lambda: self.refresh_position(False)).pack(pady=5)
        
        # 實盤帳戶掛單
        real_order_frame = ttk.LabelFrame(self.real_account_frame, text="掛單狀態", padding="5")
        real_order_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.real_order_tree = self.create_order_tree(real_order_frame)
        
        # 創建按鈕框架
        real_order_button_frame = ttk.Frame(real_order_frame)
        real_order_button_frame.pack(pady=5)
        ttk.Button(real_order_button_frame, text="刷新掛單", 
                  command=lambda: self.refresh_orders(False)).pack(side=tk.LEFT, padx=5)
        ttk.Button(real_order_button_frame, text="撤銷所有訂單",
                  command=lambda: self.cancel_all_orders(False)).pack(side=tk.LEFT, padx=5)

    def on_mode_change(self):
        """處理模式切換"""
        mode = self.mode_var.get()
        
        # 檢查所選模式是否可用
        if mode == "simulate" and not self.has_simulate_acc:
            messagebox.showerror("錯誤", "模擬帳戶未開通，請先開通模擬帳戶")
            if self.has_real_acc:
                self.mode_var.set("real")
            return
        elif mode == "real" and not self.has_real_acc:
            messagebox.showerror("錯誤", "實盤帳戶未開通或無港股交易權限")
            if self.has_simulate_acc:
                self.mode_var.set("simulate")
            return
        
        # 更新界面顯示
        if mode == "simulate":
            self.sim_account_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self.real_account_frame.pack_forget()
            self.status_var.set("當前模式：模擬交易")
            # 顯示模擬交易按鈕，隱藏實盤交易按鈕
            self.sim_frame.pack(fill=tk.X, pady=5)
            self.real_frame.pack_forget()
        else:
            self.real_account_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self.sim_account_frame.pack_forget()
            self.status_var.set("當前模式：實盤交易")
            # 顯示實盤交易按鈕，隱藏模擬交易按鈕
            self.real_frame.pack(fill=tk.X, pady=5)
            self.sim_frame.pack_forget()
        
        # 刷新相應的數據
        is_simulate = (mode == "simulate")
        self.refresh_position(is_simulate)
        self.refresh_orders(is_simulate)

    def create_position_tree(self, parent):
        """創建持倉表格"""
        tree = ttk.Treeview(parent, show="headings", height=10)
        tree["columns"] = ("code", "name", "qty", "price", "market_val", "profit")
        
        # 設置列標題
        tree.heading("code", text="代碼")
        tree.heading("name", text="名稱")
        tree.heading("qty", text="持倉數量")
        tree.heading("price", text="成本價")
        tree.heading("market_val", text="市值")
        tree.heading("profit", text="盈虧")
        
        # 設置列寬
        for col in tree["columns"]:
            tree.column(col, width=100)
        
        # 添加滾動條
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(fill=tk.BOTH, expand=True)
        return tree
        
    def check_and_open_url(self, error_msg):
        """檢查錯誤信息中是否包含URL，如果有則提供打開選項"""
        try:
            # 更完整的URL匹配模式
            url_pattern = r'https?://[^\s<>"]+|www\.[^\s<>"]+'
            urls = re.findall(url_pattern, str(error_msg))
            
            if urls:
                url = urls[0]  # 取第一個URL
                print(f"找到URL: {url}")  # 調試輸出
                
                # 確保URL是完整的
                if not url.startswith(('http://', 'https://')):
                    url = 'https://' + url
                
                if messagebox.askyesno("發現網頁鏈接", 
                                     f"錯誤信息中包含網頁鏈接，是否打開？\n{url}"):
                    try:
                        print(f"嘗試打開URL: {url}")  # 調試輸出
                        webbrowser.open(url, new=2)  # new=2表示在新標籤頁打開
                        print("URL打開成功")  # 調試輸出
                        return True
                    except Exception as e:
                        print(f"打開URL時出錯: {str(e)}")  # 調試輸出
                        messagebox.showerror("錯誤", f"無法打開網頁鏈接: {str(e)}")
                        return False
            return False
        except Exception as e:
            print(f"URL處理過程出錯: {str(e)}")  # 調試輸出
            return False

    def handle_error_message(self, title, error_msg):
        """統一處理錯誤信息"""
        # 先顯示錯誤信息
        messagebox.showerror(title, error_msg)
        # 檢查是否包含URL並處理
        self.check_and_open_url(error_msg)
        
    def connect(self):
        """連接富途API"""
        try:
            if self.trade_ctx is None:
                self.trade_ctx = OpenSecTradeContext(
                    filter_trdmarket=TrdMarket.HK, 
                    host='127.0.0.1', 
                    port=11111,
                    security_firm=SecurityFirm.FUTUSECURITIES
                )
                
                # 解鎖交易
                pwd_unlock = '789878'  # 這裡應該使用你的交易密碼
                ret, data = self.trade_ctx.unlock_trade(pwd_unlock)
                if ret != RET_OK:
                    self.status_var.set(f"解鎖交易失敗: {data}")
                    self.handle_error_message("錯誤", f"解鎖交易失敗: {data}")
                    return
                
            if self.quote_ctx is None:
                self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
                
            self.status_var.set("已連接到富途服務器並解鎖交易功能")
            
        except Exception as e:
            self.status_var.set(f"連接失敗: {str(e)}")
            self.handle_error_message("錯誤", f"連接失敗: {str(e)}")
            
            # 如果連接失敗，確保關閉所有連接
            if self.trade_ctx:
                self.trade_ctx.close()
                self.trade_ctx = None
            if self.quote_ctx:
                self.quote_ctx.close()
                self.quote_ctx = None
    
    def get_quote(self):
        """獲取股票報價"""
        try:
            code = self.code_var.get()
            ret, data = self.quote_ctx.get_market_snapshot([code])
            if ret == RET_OK:
                price = data['last_price'][0]
                self.price_var.set(f"{price:.3f}")
                self.status_var.set(f"已獲取 {code} 最新報價")
            else:
                self.status_var.set(f"獲取報價失敗: {data}")
        except Exception as e:
            self.status_var.set(f"獲取報價出錯: {str(e)}")
    
    def place_order(self, direction, is_simulate, from_api=False):
        """下單"""
        # 檢查當前模式是否匹配
        current_mode = self.mode_var.get()
        if (current_mode == "simulate" and not is_simulate) or (current_mode == "real" and is_simulate):
            messagebox.showerror("錯誤", "請切換到對應的交易模式後再操作")
            return
            
        try:
            # 獲取輸入值
            code = self.code_var.get()
            price = float(self.price_var.get())
            qty = int(self.qty_var.get())
            
            # 設置交易環境
            trd_env = TrdEnv.SIMULATE if is_simulate else TrdEnv.REAL
            env_name = "模擬" if is_simulate else "實盤"
            
            # 設置買賣方向
            trd_side = TrdSide.BUY if direction == "BUY" else TrdSide.SELL
            
            # 下單前確認 (如果不是來自API則顯示確認對話框)
            if not is_simulate and not from_api:
                if not messagebox.askyesno("確認", 
                    f"確定要在實盤{direction}嗎？\n股票：{code}\n價格：{price}\n數量：{qty}"):
                    return
            
            # 如果來自API, 記錄訊息
            if from_api:
                api_source = f"(來自外部API: {self.api_symbol_var.get()})"
            else:
                api_source = ""
            
            # 下單
            ret, data = self.trade_ctx.place_order(
                price=price,
                qty=qty,
                code=code,
                trd_side=trd_side,
                order_type=OrderType.NORMAL,
                trd_env=trd_env
            )
            
            if ret == RET_OK:
                order_id = data['order_id'][0]
                success_msg = f"{env_name}下單成功！訂單號: {order_id} {api_source}"
                
                if from_api:
                    self.status_var.set(success_msg)
                else:
                    messagebox.showinfo("成功", success_msg)
                
                self.status_var.set(f"{env_name}下單成功: {direction} {code} {qty}股 @ {price} {api_source}")
                # 刷新相應的持倉和掛單
                self.refresh_position(is_simulate)
                self.refresh_orders(is_simulate)
            else:
                error_msg = f"{env_name}下單失敗: {data} {api_source}"
                
                if from_api:
                    self.status_var.set(error_msg)
                else:
                    messagebox.showerror("錯誤", error_msg)
                
        except ValueError:
            error_msg = "請輸入有效的價格和數量"
            if not from_api:
                self.handle_error_message("錯誤", error_msg)
            else:
                self.status_var.set(error_msg)
        except Exception as e:
            error_msg = f"下單失敗: {str(e)}"
            if not from_api:
                self.handle_error_message("錯誤", error_msg)
            else:
                self.status_var.set(error_msg)
    
    def check_account_status(self):
        """檢查帳戶狀態"""
        try:
            if self.trade_ctx:
                ret, data = self.trade_ctx.get_acc_list()
                if ret == RET_OK:
                    self.has_simulate_acc = False
                    self.has_real_acc = False
                    
                    for _, row in data.iterrows():
                        if row['trd_env'] == TrdEnv.SIMULATE:
                            self.has_simulate_acc = True
                        elif row['trd_env'] == TrdEnv.REAL:
                            self.has_real_acc = True
                    
                    # 更新界面狀態
                    self.update_ui_by_account_status()
                    
                    # 顯示帳戶狀態
                    status_msg = []
                    if not self.has_simulate_acc:
                        status_msg.append("模擬帳戶未開通")
                    if not self.has_real_acc:
                        status_msg.append("實盤帳戶未開通")
                    
                    if status_msg:
                        self.status_var.set(" | ".join(status_msg))
                        messagebox.showwarning("帳戶狀態", "\n".join(status_msg))
                else:
                    self.status_var.set(f"檢查帳戶狀態失敗: {data}")
        except Exception as e:
            self.status_var.set(f"檢查帳戶狀態出錯: {str(e)}")

    def update_ui_by_account_status(self):
        """根據帳戶狀態更新界面"""
        # 更新模擬交易按鈕狀態
        for child in self.sim_frame.winfo_children():
            if isinstance(child, ttk.Button):
                child.configure(state='normal' if self.has_simulate_acc else 'disabled')
        
        # 更新實盤交易按鈕狀態
        for child in self.real_frame.winfo_children():
            if isinstance(child, ttk.Button):
                child.configure(state='normal' if self.has_real_acc else 'disabled')
        
        # 更新模式選擇按鈕狀態
        for child in self.mode_frame.winfo_children():
            if isinstance(child, ttk.Radiobutton):
                if "模擬" in child["text"] and not self.has_simulate_acc:
                    child.configure(state='disabled')
                elif "實盤" in child["text"] and not self.has_real_acc:
                    child.configure(state='disabled')
                else:
                    child.configure(state='normal')
        
        # 如果當前選擇的模式不可用，切換到可用的模式
        current_mode = self.mode_var.get()
        if current_mode == "simulate" and not self.has_simulate_acc:
            if self.has_real_acc:
                self.mode_var.set("real")
                self.on_mode_change()
        elif current_mode == "real" and not self.has_real_acc:
            if self.has_simulate_acc:
                self.mode_var.set("simulate")
                self.on_mode_change()

    def refresh_position(self, is_simulate):
        """刷新持倉信息"""
        try:
            # 選擇要更新的表格
            tree = self.sim_position_tree if is_simulate else self.real_position_tree
            env_name = "模擬" if is_simulate else "實盤"
            
            # 清空現有數據
            for item in tree.get_children():
                tree.delete(item)
            
            # 獲取持倉數據
            trd_env = TrdEnv.SIMULATE if is_simulate else TrdEnv.REAL
            ret, data = self.trade_ctx.position_list_query(trd_env=trd_env)
            
            if ret == RET_OK:
                if len(data) > 0:
                    for _, row in data.iterrows():
                        tree.insert('', 'end', values=(
                            row['code'],
                            row['stock_name'],
                            row['qty'],
                            f"{row['cost_price']:.3f}",
                            f"{row['market_val']:.2f}",
                            f"{row['pl_ratio']:.2f}%"
                        ))
                else:
                    # 如果沒有持倉，顯示提示信息
                    tree.insert('', 'end', values=(
                        "無持倉", "", "", "", "", ""
                    ))
                self.status_var.set(f"{env_name}持倉數據已更新")
            else:
                self.status_var.set(f"獲取{env_name}持倉失敗: {data}")
                if "未開通" in str(data) or "無權限" in str(data):
                    tree.insert('', 'end', values=(
                        f"請先開通{env_name}帳戶", "", "", "", "", ""
                    ))
                
        except Exception as e:
            self.status_var.set(f"刷新{env_name}持倉出錯: {str(e)}")
            tree.insert('', 'end', values=(
                f"刷新出錯: {str(e)}", "", "", "", "", ""
            ))
    
    def create_order_tree(self, parent):
        """創建掛單表格"""
        tree = ttk.Treeview(parent, show="headings", height=5)
        tree["columns"] = ("order_id", "code", "trd_side", "qty", "price", "status", "create_time")
        
        # 設置列標題
        tree.heading("order_id", text="訂單號")
        tree.heading("code", text="代碼")
        tree.heading("trd_side", text="方向")
        tree.heading("qty", text="數量")
        tree.heading("price", text="價格")
        tree.heading("status", text="狀態")
        tree.heading("create_time", text="創建時間")
        
        # 設置列寬
        tree.column("order_id", width=80)
        tree.column("code", width=80)
        tree.column("trd_side", width=50)
        tree.column("qty", width=60)
        tree.column("price", width=70)
        tree.column("status", width=80)
        tree.column("create_time", width=130)
        
        # 添加滾動條
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 綁定右鍵菜單
        tree.bind("<Button-3>", self.show_order_menu)
        
        tree.pack(fill=tk.BOTH, expand=True)
        return tree
        
    def show_order_menu(self, event):
        """顯示訂單右鍵菜單"""
        tree = event.widget
        item = tree.identify_row(event.y)
        if item:
            tree.selection_set(item)
            # 獲取訂單狀態
            values = tree.item(item)['values']
            if values and len(values) >= 6:  # 確保有足夠的值
                status = values[5]  # 狀態在第6列
                # 只有當訂單狀態為"已提交"或"部分成交"時才顯示撤單選項
                if status in ["已提交", "部分成交"]:
                    menu = tk.Menu(tree, tearoff=0)
                    menu.add_command(label="撤單", 
                                   command=lambda: self.cancel_order(tree))
                    menu.post(event.x_root, event.y_root)
    
    def cancel_order(self, tree):
        """撤銷選中的訂單"""
        selection = tree.selection()
        if not selection:
            return
            
        item = selection[0]
        values = tree.item(item)['values']
        if not values:
            return
            
        order_id = values[0]  # 訂單號在第一列
        is_simulate = self.mode_var.get() == "simulate"
        env_name = "模擬" if is_simulate else "實盤"
        
        # 確認撤單
        if not messagebox.askyesno("確認撤單", 
                                 f"確定要撤銷{env_name}訂單 {order_id} 嗎？"):
            return
        
        try:
            # 撤單
            ret, data = self.trade_ctx.modify_order(
                modify_order_op=ModifyOrderOp.CANCEL,
                order_id=str(order_id),
                qty=0,  # 撤單時數量參數無效
                price=0.0,  # 撤單時價格參數無效
                trd_env=TrdEnv.SIMULATE if is_simulate else TrdEnv.REAL
            )
            
            if ret == RET_OK:
                messagebox.showinfo("成功", f"{env_name}撤單成功！")
                self.status_var.set(f"{env_name}撤單成功: {order_id}")
                # 刷新掛單列表
                self.refresh_orders(is_simulate)
            else:
                error_msg = f"{env_name}撤單失敗: {data}"
                self.status_var.set(error_msg)
                messagebox.showerror("錯誤", error_msg)
                
        except Exception as e:
            error_msg = f"{env_name}撤單出錯: {str(e)}"
            self.status_var.set(error_msg)
            messagebox.showerror("錯誤", error_msg)

    def refresh_orders(self, is_simulate):
        """刷新掛單狀態"""
        try:
            # 選擇要更新的表格
            tree = self.sim_order_tree if is_simulate else self.real_order_tree
            env_name = "模擬" if is_simulate else "實盤"
            
            # 清空現有數據
            for item in tree.get_children():
                tree.delete(item)
            
            # 獲取掛單數據
            trd_env = TrdEnv.SIMULATE if is_simulate else TrdEnv.REAL
            
            # 查詢所有狀態的訂單
            ret, data = self.trade_ctx.order_list_query(
                trd_env=trd_env,
                status_filter_list=[
                    OrderStatus.NONE,           # 未知
                    OrderStatus.WAITING_SUBMIT, # 等待提交
                    OrderStatus.SUBMITTING,     # 正在提交
                    OrderStatus.SUBMITTED,      # 已提交
                    OrderStatus.FILLED_PART,    # 部分成交
                    OrderStatus.FILLED_ALL,     # 全部成交
                    OrderStatus.CANCELLED_ALL,  # 已撤單
                    OrderStatus.CANCELLED_PART, # 部分撤單
                    OrderStatus.SUBMIT_FAILED,  # 提交失敗
                    OrderStatus.FAILED,         # 下單失敗
                    OrderStatus.DISABLED        # 已失效
                ],
                code="",  # 空字符串表示查詢所有股票
                start="",  # 空字符串表示不設起始時間限制
                end=""     # 空字符串表示不設結束時間限制
            )
            
            if ret == RET_OK:
                if len(data) > 0:
                    # 按創建時間降序排序
                    data = data.sort_values(by='create_time', ascending=False)
                    
                    for _, row in data.iterrows():
                        # 轉換交易方向
                        trd_side = "買入" if row['trd_side'] == TrdSide.BUY else "賣出"
                        
                        # 轉換訂單狀態
                        status_map = {
                            OrderStatus.NONE: "未知",
                            OrderStatus.WAITING_SUBMIT: "等待提交",
                            OrderStatus.SUBMITTING: "正在提交",
                            OrderStatus.SUBMITTED: "已提交",
                            OrderStatus.FILLED_PART: "部分成交",
                            OrderStatus.FILLED_ALL: "全部成交",
                            OrderStatus.CANCELLED_ALL: "已撤單",
                            OrderStatus.CANCELLED_PART: "部分撤單",
                            OrderStatus.SUBMIT_FAILED: "提交失敗",
                            OrderStatus.FAILED: "下單失敗",
                            OrderStatus.DISABLED: "已失效"
                        }
                        status = status_map.get(row['order_status'], "未知")
                        
                        # 格式化時間
                        create_time = row['create_time']
                        if isinstance(create_time, str):
                            try:
                                create_time = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                pass
                        
                        formatted_time = create_time.strftime('%Y-%m-%d %H:%M:%S') if isinstance(create_time, datetime) else str(create_time)
                        
                        tree.insert('', 'end', values=(
                            row['order_id'],
                            row['code'],
                            trd_side,
                            row['qty'],
                            f"{row['price']:.3f}",
                            status,
                            formatted_time
                        ))
                        
                    self.status_var.set(f"{env_name}掛單數據已更新 - 共{len(data)}條記錄")
                else:
                    tree.insert('', 'end', values=(
                        "無掛單記錄", "", "", "", "", "", ""
                    ))
                    self.status_var.set(f"{env_name}掛單數據已更新 - 無記錄")
            else:
                error_msg = f"獲取{env_name}掛單失敗: {data}"
                self.status_var.set(error_msg)
                print(error_msg)  # 輸出到控制台以便調試
                
                if "未開通" in str(data) or "無權限" in str(data):
                    tree.insert('', 'end', values=(
                        f"請先開通{env_name}帳戶", "", "", "", "", "", ""
                    ))
                    self.handle_error_message("錯誤", error_msg)
                else:
                    tree.insert('', 'end', values=(
                        f"獲取數據失敗: {str(data)}", "", "", "", "", "", ""
                    ))
                    self.handle_error_message("錯誤", error_msg)
                
        except Exception as e:
            error_msg = f"刷新{env_name}掛單出錯: {str(e)}"
            self.status_var.set(error_msg)
            print(error_msg)  # 輸出到控制台以便調試
            tree.insert('', 'end', values=(
                f"刷新出錯: {str(e)}", "", "", "", "", "", ""
            ))
            self.handle_error_message("錯誤", error_msg)
    
    def on_closing(self):
        """處理窗口關閉事件"""
        try:
            # 關閉API服務器
            self.stop_api_server()
            
            if self.trade_ctx:
                self.trade_ctx.close()
            if self.quote_ctx:
                self.quote_ctx.close()
        except Exception as e:
            print(f"關閉連接時出錯: {str(e)}")
        finally:
            self.root.destroy()
            sys.exit(0)  # 確保程式完全退出

    def cancel_all_orders(self, is_simulate):
        """撤銷所有訂單"""
        env_name = "模擬" if is_simulate else "實盤"
        
        # 確認撤單
        if not messagebox.askyesno("確認撤銷所有訂單", 
                                 f"確定要撤銷所有{env_name}訂單嗎？"):
            return
            
        try:
            # 獲取當前所有訂單
            trd_env = TrdEnv.SIMULATE if is_simulate else TrdEnv.REAL
            ret, data = self.trade_ctx.order_list_query(
                trd_env=trd_env,
                status_filter_list=[
                    OrderStatus.NONE,           # 未知
                    OrderStatus.WAITING_SUBMIT, # 等待提交
                    OrderStatus.SUBMITTING,     # 正在提交
                    OrderStatus.SUBMITTED,      # 已提交
                    OrderStatus.FILLED_PART,    # 部分成交
                    #OrderStatus.FILLED_ALL,     # 全部成交
                    #OrderStatus.CANCELLED_ALL,  # 已撤單
                    OrderStatus.CANCELLED_PART, # 部分撤單
                    OrderStatus.SUBMIT_FAILED,  # 提交失敗
                    #OrderStatus.FAILED,         # 下單失敗
                    #OrderStatus.DISABLED        # 已失效
                ],
                code="",  # 空字符串表示查詢所有股票
                start="",  # 空字符串表示不設起始時間限制
                end=""     # 空字符串表示不設結束時間限制
            )
            
            if ret == RET_OK:
                if len(data) > 0:
                    success_count = 0
                    fail_count = 0
                    error_msgs = []
                    
                    for _, row in data.iterrows():
                        order_id = str(row['order_id'])
                        ret, data = self.trade_ctx.modify_order(
                            modify_order_op=ModifyOrderOp.CANCEL,
                            order_id=order_id,
                            qty=0,  # 撤單時數量參數無效
                            price=0.0,  # 撤單時價格參數無效
                            trd_env=trd_env
                        )

                        
                        if ret == RET_OK:
                            success_count += 1
                        else:
                            fail_count += 1
                            error_msgs.append(f"訂單 {order_id}: {data}")
                    
                    # 顯示結果
                    result_msg = f"{env_name}撤單完成\n成功: {success_count} 筆\n失敗: {fail_count} 筆"
                    if error_msgs:
                        result_msg += "\n\n失敗詳情:\n" + "\n".join(error_msgs)
                    
                    if fail_count > 0:
                        messagebox.showerror("撤單結果", result_msg)
                    else:
                        messagebox.showinfo("撤單結果", result_msg)
                    
                    # 刷新掛單列表
                    self.refresh_orders(is_simulate)
                else:
                    messagebox.showinfo("提示", f"當前沒有可撤銷的{env_name}訂單")
            else:
                error_msg = f"獲取{env_name}訂單列表失敗: {data}"
                self.status_var.set(error_msg)
                messagebox.showerror("錯誤", error_msg)
                
        except Exception as e:
            error_msg = f"撤銷{env_name}訂單出錯: {str(e)}"
            self.status_var.set(error_msg)
            messagebox.showerror("錯誤", error_msg)

    def toggle_api_server(self):
        """切換API服務器狀態"""
        if self.api_enabled_var.get():
            self.start_api_server()
        else:
            self.stop_api_server()
    
    def start_api_server(self):
        """啟動API服務器"""
        if self.api_server is not None:
            self.stop_api_server()
            
        try:
            # 創建處理外部API請求的處理器
            class ApiHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, app=None, **kwargs):
                    self.app = app
                    super().__init__(*args, **kwargs)
                
                def log_message(self, format, *args):
                    # 禁止打印訪問日誌
                    pass
                
                def do_GET(self):
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    self.wfile.write(b"API Server is running. Available endpoints: /order, /flatten")
                
                def do_POST(self):
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length)
                    
                    try:
                        # 解析JSON數據
                        data = json.loads(post_data.decode('utf-8'))
                        
                        # 根據請求路徑處理不同類型的請求
                        if self.path == "/order":
                            # 處理下單請求
                            self.app.handle_api_order(data)
                            response = {"status": "ok", "message": "Order request processed"}
                        elif self.path == "/flatten":
                            # 處理平倉請求
                            self.app.handle_api_flatten(data)
                            response = {"status": "ok", "message": "Flatten request processed"}
                        else:
                            # 未知的請求路徑
                            response = {"status": "error", "message": f"Unknown endpoint: {self.path}. Available endpoints: /order, /flatten"}
                            self.send_response(404)
                            self.send_header('Content-type', 'application/json')
                            self.end_headers()
                            self.wfile.write(json.dumps(response).encode('utf-8'))
                            return
                        
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps(response).encode('utf-8'))
                    except Exception as e:
                        self.app.status_var.set(f"處理API請求出錯: {str(e)}")
                        
                        self.send_response(400)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({"status": "error", "message": str(e)}).encode('utf-8'))
            
            # 使用自定義處理器並傳入應用實例
            handler = lambda *args, **kwargs: ApiHandler(*args, app=self, **kwargs)
            
            # 創建服務器
            self.api_server = socketserver.TCPServer(("", self.api_port), handler)
            
            # 在新線程中啟動服務器
            self.api_thread = threading.Thread(target=self.api_server.serve_forever)
            self.api_thread.daemon = True  # 設置為守護線程，隨主線程退出
            self.api_thread.start()
            
            self.api_status_var.set(f"API服務器: 已啟動 ")
            self.status_var.set(f"外部API服務器已啟動 ")
        
        except Exception as e:
            self.api_enabled_var.set(False)
            self.api_status_var.set(f"API服務器: 啟動失敗")
            self.status_var.set(f"啟動API服務器出錯: {str(e)}")
            messagebox.showerror("錯誤", f"啟動API服務器失敗: {str(e)}")
    
    def stop_api_server(self):
        """停止API服務器"""
        if self.api_server is not None:
            try:
                self.api_server.shutdown()
                self.api_server.server_close()
                self.api_server = None
                self.api_thread = None
                self.api_status_var.set("API服務器: 已停止")
                self.status_var.set("外部API服務器已停止")
            except Exception as e:
                self.status_var.set(f"停止API服務器出錯: {str(e)}")
    
    def handle_api_signal(self, data):
        """處理來自外部API的信號 (舊版本兼容方法)"""
        self.handle_api_order(data)
    
    def handle_api_order(self, data):
        """處理來自外部API的下單信號"""
        try:
            # 檢查必要參數
            if 'symbol' not in data or 'direction' not in data:
                raise ValueError("缺少必要參數: symbol或direction")
            
            # 獲取外部API的商品和方向
            api_symbol = data['symbol'].lower()
            direction = data['direction'].upper()
            
            # 獲取當前設置的API商品
            target_api_symbol = self.api_symbol_var.get().lower()
            
            # 檢查是否匹配目標商品
            if api_symbol != target_api_symbol:
                self.status_var.set(f"忽略非目標商品信號: {api_symbol} (目標: {target_api_symbol})")
                return
            
            # 檢查方向是否有效
            if direction not in ["BUY", "SELL"]:
                raise ValueError(f"無效的交易方向: {direction}")
            
            # 獲取當前交易模式
            is_simulate = self.mode_var.get() == "simulate"
            
            # 獲取輸入的交易商品和數量
            code = self.code_var.get()
            qty = int(self.qty_var.get())
            
            # 處理價格映射
            if (DERIVATIVE_MAPPING_AVAILABLE and
                self.derivative_mapper and
                hasattr(self, 'use_derivative_mapping_var') and
                self.use_derivative_mapping_var.get()):

                # 使用衍生品價格映射
                try:
                    # 從API數據中獲取原商品價格
                    underlying_price = data.get('price', 40000)  # 默認價格
                    if isinstance(underlying_price, str):
                        underlying_price = float(underlying_price)

                    # 獲取映射ID
                    mapping_id = self.derivative_mapping_id_var.get().split(' ')[0]

                    # 執行價格映射
                    mapping_result = self.derivative_mapper.map_price(
                        api_symbol, underlying_price, mapping_id, use_black_scholes=True
                    )

                    if mapping_result:
                        # 更新股票代碼為衍生品代碼
                        self.code_var.set(mapping_result['derivative_code'])
                        # 更新價格為計算出的衍生品價格
                        self.price_var.set(f"{mapping_result['derivative_price']:.3f}")

                        msg = f"收到外部API信號: {api_symbol}={underlying_price} -> {mapping_result['derivative_code']}={mapping_result['derivative_price']:.3f}"
                        self.status_var.set(msg)
                        print(msg)
                    else:
                        raise Exception("價格映射失敗")

                except Exception as e:
                    error_msg = f"衍生品價格映射失敗: {str(e)}"
                    self.status_var.set(error_msg)
                    print(error_msg)
                    return
            else:
                # 使用原有邏輯：從報價獲取價格
                self.get_quote()
                price = float(self.price_var.get())

                msg = f"收到外部API下單信號: 商品={api_symbol}, 方向={direction}"
                self.status_var.set(msg)
                print(msg)

            # 處理智能SL/TP映射
            if (SMART_MAPPING_AVAILABLE and
                self.smart_mapper and
                hasattr(self, 'use_smart_mapping_var') and
                self.use_smart_mapping_var.get()):

                # 檢查是否有SL/TP數據
                sl_price = data.get('sl', 0)
                tp_price = data.get('tp', 0)

                if sl_price and tp_price:
                    try:
                        sl_price = float(sl_price)
                        tp_price = float(tp_price)

                        # 執行智能SL/TP映射（傳遞完整的外部數據）
                        mapping_result = self.smart_mapper.map_sl_tp_prices(
                            api_symbol, sl_price, tp_price, external_data=data
                        )

                        if mapping_result:
                            # 更新SL/TP輸入框
                            self.sl_var.set(f"{mapping_result['warrant_sl']:.3f}")
                            self.tp_var.set(f"{mapping_result['warrant_tp']:.3f}")

                            # 更新狀態
                            self.sl_tp_status_var.set(f"智能映射: SL={mapping_result['warrant_sl']:.3f} TP={mapping_result['warrant_tp']:.3f}")

                            msg += f" | SL/TP已映射"
                            self.status_var.set(msg)
                            print(f"SL/TP映射: {sl_price}/{tp_price} -> {mapping_result['warrant_sl']:.3f}/{mapping_result['warrant_tp']:.3f}")

                    except Exception as e:
                        print(f"智能SL/TP映射失敗: {e}")

            # 執行下單
            self.place_order(direction, is_simulate, from_api=True)
            
        except Exception as e:
            error_msg = f"處理API下單信號出錯: {str(e)}"
            self.status_var.set(error_msg)
            print(error_msg)
    
    def handle_api_flatten(self, data):
        """處理來自外部API的平倉信號"""
        try:
            # 檢查必要參數
            if 'symbol' not in data:
                raise ValueError("缺少必要參數: symbol")
            
            # 獲取外部API的商品
            api_symbol = data['symbol'].lower()
            
            # 獲取當前設置的API商品
            target_api_symbol = self.api_symbol_var.get().lower()
            
            # 檢查是否匹配目標商品
            if api_symbol != target_api_symbol:
                self.status_var.set(f"忽略非目標商品平倉信號: {api_symbol} (目標: {target_api_symbol})")
                return
            
            # 獲取當前交易模式
            is_simulate = self.mode_var.get() == "simulate"
            
            # 獲取要平倉的富途商品代碼
            code = self.code_var.get()
            
            # 記錄信號接收
            msg = f"收到外部API平倉信號: 商品={api_symbol}, 目標平倉股票={code}"
            self.status_var.set(msg)
            print(msg)
            
            # 執行平倉操作
            self.flatten_position(code, is_simulate)
            
        except Exception as e:
            error_msg = f"處理API平倉信號出錯: {str(e)}"
            self.status_var.set(error_msg)
            print(error_msg)
    
    def flatten_position(self, code, is_simulate):
        """平倉指定股票的所有倉位"""
        try:
            # 設置交易環境
            trd_env = TrdEnv.SIMULATE if is_simulate else TrdEnv.REAL
            env_name = "模擬" if is_simulate else "實盤"
            
            # 獲取持倉數據
            ret, data = self.trade_ctx.position_list_query(trd_env=trd_env, code=code)
            
            if ret != RET_OK:
                raise Exception(f"獲取持倉數據失敗: {data}")
            
            # 檢查是否有持倉
            if len(data) == 0:
                self.status_var.set(f"{env_name}無{code}持倉，無需平倉")
                return
            
            # 處理每個持倉
            for _, row in data.iterrows():
                position_code = row['code']
                position_qty = int(row['qty'])
                
                if position_qty == 0:
                    continue
                
                # 根據持倉方向決定平倉方向
                trd_side = TrdSide.SELL if row['position_side'] == PositionSide.LONG else TrdSide.BUY
                direction = "SELL" if trd_side == TrdSide.SELL else "BUY"
                
                # 獲取最新價格
                self.code_var.set(position_code)
                self.get_quote()
                price = float(self.price_var.get())
                
                # 設置數量
                self.qty_var.set(str(position_qty))
                
                # 執行平倉訂單
                ret, data = self.trade_ctx.place_order(
                    price=price,
                    qty=position_qty,
                    code=position_code,
                    trd_side=trd_side,
                    order_type=OrderType.NORMAL,
                    trd_env=trd_env
                )
                
                if ret == RET_OK:
                    order_id = data['order_id'][0]
                    success_msg = f"{env_name}平倉成功！訂單號: {order_id} (來自API平倉請求)"
                    self.status_var.set(success_msg)
                    print(success_msg)
                else:
                    error_msg = f"{env_name}平倉失敗: {data}"
                    self.status_var.set(error_msg)
                    print(error_msg)
            
            # 刷新相應的持倉和掛單
            self.refresh_position(is_simulate)
            self.refresh_orders(is_simulate)
            
        except Exception as e:
            error_msg = f"平倉操作出錯: {str(e)}"
            self.status_var.set(error_msg)
            print(error_msg)

    def toggle_smart_mapping(self):
        """切換智能映射狀態"""
        if not SMART_MAPPING_AVAILABLE:
            return

        if self.use_smart_mapping_var.get():
            self.smart_status_var.set("智能映射已啟用")
        else:
            self.smart_status_var.set("智能映射已禁用")
            if self.smart_mapper:
                self.smart_mapper.stop_monitoring()

    def setup_smart_mapping(self):
        """設置智能映射"""
        if not SMART_MAPPING_AVAILABLE or not self.smart_mapper:
            messagebox.showerror("錯誤", "智能映射功能不可用")
            return

        try:
            underlying_symbol = self.api_symbol_var.get()
            warrant_code = self.warrant_code_var.get()
            warrant_name = self.warrant_name_var.get()

            if not underlying_symbol or not warrant_code:
                messagebox.showerror("錯誤", "請輸入原商品代碼和認購證代碼")
                return

            # 添加映射
            mapping_id = f"smart_mapping_{int(time.time())}"
            self.smart_mapper.add_mapping(
                mapping_id, underlying_symbol, warrant_code, warrant_name
            )

            self.smart_status_var.set(f"映射已設置: {underlying_symbol} -> {warrant_code}")
            messagebox.showinfo("成功", "智能映射設置成功")

        except Exception as e:
            messagebox.showerror("錯誤", f"設置智能映射失敗: {str(e)}")

    def get_market_snapshots(self):
        """獲取市價快照"""
        if not SMART_MAPPING_AVAILABLE or not self.smart_mapper:
            messagebox.showerror("錯誤", "智能映射功能不可用")
            return

        try:
            underlying_symbol = self.api_symbol_var.get()
            warrant_code = self.warrant_code_var.get()

            if not underlying_symbol or not warrant_code:
                messagebox.showerror("錯誤", "請先設置映射關係")
                return

            # 獲取快照
            underlying_snapshot = self.smart_mapper.get_market_snapshot(underlying_symbol)
            warrant_snapshot = self.smart_mapper.get_market_snapshot(warrant_code)

            if underlying_snapshot and warrant_snapshot:
                msg = f"快照已獲取 | {underlying_symbol}: {underlying_snapshot['last_price']:.3f} | {warrant_code}: {warrant_snapshot['last_price']:.3f}"
                self.smart_status_var.set(msg)
                messagebox.showinfo("成功", "市價快照獲取成功")
            else:
                messagebox.showerror("錯誤", "獲取市價快照失敗")

        except Exception as e:
            messagebox.showerror("錯誤", f"獲取市價快照失敗: {str(e)}")

    def start_price_monitoring(self):
        """開始價格監控"""
        if not SMART_MAPPING_AVAILABLE or not self.smart_mapper:
            messagebox.showerror("錯誤", "智能映射功能不可用")
            return

        try:
            # 查找映射ID
            mappings = self.smart_mapper.get_all_mappings()
            if not mappings:
                messagebox.showerror("錯誤", "請先設置映射關係")
                return

            # 使用第一個映射開始監控
            mapping_id = list(mappings.keys())[0]
            self.smart_mapper.start_monitoring(mapping_id)

            self.smart_status_var.set("價格監控已開始")
            messagebox.showinfo("成功", "價格監控已開始")

        except Exception as e:
            messagebox.showerror("錯誤", f"開始價格監控失敗: {str(e)}")

    def update_sl_tp_from_mapping(self, mapping_result: dict):
        """從映射結果更新SL/TP"""
        try:
            if mapping_result:
                # 更新SL/TP輸入框
                self.sl_var.set(f"{mapping_result['warrant_sl']:.3f}")
                self.tp_var.set(f"{mapping_result['warrant_tp']:.3f}")

                # 更新認購證代碼
                self.code_var.set(mapping_result['warrant_code'])

                # 更新狀態
                status_msg = f"智能映射完成: {mapping_result['underlying_symbol']} -> {mapping_result['warrant_code']}"
                self.sl_tp_status_var.set(status_msg)

                print(f"SL/TP自動更新: SL={mapping_result['warrant_sl']:.3f}, TP={mapping_result['warrant_tp']:.3f}")

        except Exception as e:
            print(f"更新SL/TP失敗: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = TradeApp(root)
    root.mainloop()