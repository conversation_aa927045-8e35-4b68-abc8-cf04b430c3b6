"""
主窗口
整合所有功能模塊的主界面
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os
from typing import Optional

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core import ConfigManager, FutuClient, DataManager
# 延遲導入以避免循環導入問題
# from .trading_panel import TradingPanel
# from .kline_panel import KLinePanel
# from .monitoring_panel import MonitoringPanel

class MainWindow:
    """主窗口類"""
    
    def __init__(self):
        """初始化主窗口"""
        # 初始化核心組件
        self.config_manager = ConfigManager()
        self.data_manager = DataManager(self.config_manager.get('gui.data_save_path', './data/'))
        self.futu_client = FutuClient(self.config_manager.get_futu_config())
        
        # 創建主窗口
        self.root = tk.Tk()
        self.setup_window()
        
        # 設置富途客戶端回調
        self.futu_client.set_callbacks(
            status_callback=self.update_status,
            error_callback=self.handle_error
        )
        
        # 狀態變量
        self.status_var = tk.StringVar(value="未連接")
        
        # 創建界面
        self.create_widgets()
        
        # 註冊關閉事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_window(self):
        """設置窗口屬性"""
        gui_config = self.config_manager.get_gui_config()
        
        self.root.title(gui_config.get('window_title', '富途量化交易系統'))
        self.root.geometry(gui_config.get('window_size', '1400x900'))
        
        # 設置窗口圖標（如果有的話）
        try:
            # self.root.iconbitmap('icon.ico')  # 可以添加圖標文件
            pass
        except:
            pass
        
        # 設置最小窗口大小
        self.root.minsize(1200, 800)
    
    def create_widgets(self):
        """創建界面組件"""
        # 創建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 創建頂部工具欄
        self.create_toolbar(main_frame)
        
        # 創建狀態欄
        self.create_statusbar(main_frame)
        
        # 創建主要內容區域
        self.create_main_content(main_frame)
    
    def create_toolbar(self, parent):
        """創建工具欄"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 連接按鈕
        self.connect_btn = ttk.Button(
            toolbar_frame, text="連接富途", command=self.connect_futu
        )
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 斷開連接按鈕
        self.disconnect_btn = ttk.Button(
            toolbar_frame, text="斷開連接", command=self.disconnect_futu, state=tk.DISABLED
        )
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 設置按鈕
        ttk.Button(
            toolbar_frame, text="設置", command=self.open_settings
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 數據管理按鈕
        ttk.Button(
            toolbar_frame, text="數據管理", command=self.open_data_manager
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 幫助按鈕
        ttk.Button(
            toolbar_frame, text="幫助", command=self.show_help
        ).pack(side=tk.RIGHT)
        
        # 關於按鈕
        ttk.Button(
            toolbar_frame, text="關於", command=self.show_about
        ).pack(side=tk.RIGHT, padx=(0, 5))
    
    def create_statusbar(self, parent):
        """創建狀態欄"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        
        # 狀態標籤
        ttk.Label(status_frame, text="狀態:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=(5, 0))
        
        # 時間標籤
        self.time_var = tk.StringVar()
        ttk.Label(status_frame, textvariable=self.time_var).pack(side=tk.RIGHT)
        
        # 更新時間
        self.update_time()
    
    def create_main_content(self, parent):
        """創建主要內容區域"""
        # 創建筆記本控件（標籤頁）
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 創建各個功能面板
        self.create_panels()

    def create_panels(self):
        """創建功能面板"""
        try:
            # 動態導入面板類以避免循環導入
            from .trading_panel import TradingPanel
            from .kline_panel import KLinePanel
            from .monitoring_panel import MonitoringPanel

            # 交易面板
            self.trading_panel = TradingPanel(
                self.notebook, self.futu_client, self.config_manager, self.data_manager
            )
            self.notebook.add(self.trading_panel.frame, text="交易")

            # K線面板
            self.kline_panel = KLinePanel(
                self.notebook, self.futu_client, self.config_manager, self.data_manager
            )
            self.notebook.add(self.kline_panel.frame, text="K線圖表")

            # 監控面板
            self.monitoring_panel = MonitoringPanel(
                self.notebook, self.futu_client, self.config_manager, self.data_manager
            )
            self.notebook.add(self.monitoring_panel.frame, text="實時監控")
        except Exception as e:
            print(f"創建面板時出錯: {e}")
            import traceback
            traceback.print_exc()
            # 創建簡單的錯誤提示面板
            error_frame = ttk.Frame(self.notebook)
            ttk.Label(error_frame, text=f"面板加載失敗: {e}").pack(pady=20)
            self.notebook.add(error_frame, text="錯誤")
    
    def connect_futu(self):
        """連接富途API"""
        if self.futu_client.connect():
            self.connect_btn.config(state=tk.DISABLED)
            self.disconnect_btn.config(state=tk.NORMAL)
            
            # 通知各面板連接狀態
            self.trading_panel.on_connection_changed(True)
            self.kline_panel.on_connection_changed(True)
            self.monitoring_panel.on_connection_changed(True)
    
    def disconnect_futu(self):
        """斷開富途連接"""
        self.futu_client.disconnect()
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)
        
        # 通知各面板連接狀態
        self.trading_panel.on_connection_changed(False)
        self.kline_panel.on_connection_changed(False)
        self.monitoring_panel.on_connection_changed(False)
    
    def update_status(self, message: str):
        """更新狀態"""
        self.status_var.set(message)
        self.data_manager.save_log("INFO", message, "system")
    
    def handle_error(self, title: str, message: str):
        """處理錯誤"""
        messagebox.showerror(title, message)
        self.data_manager.save_log("ERROR", f"{title}: {message}", "system")
    
    def update_time(self):
        """更新時間顯示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self.update_time)
    
    def open_settings(self):
        """打開設置窗口"""
        # TODO: 實現設置窗口
        messagebox.showinfo("設置", "設置功能開發中...")
    
    def open_data_manager(self):
        """打開數據管理窗口"""
        # TODO: 實現數據管理窗口
        messagebox.showinfo("數據管理", "數據管理功能開發中...")
    
    def show_help(self):
        """顯示幫助"""
        help_text = """
富途量化交易系統 v2.0 使用說明

1. 連接設置
   - 確保FutuOpenD已啟動
   - 點擊"連接富途"按鈕建立連接
   - 在設置中配置交易密碼以解鎖交易功能

2. 交易功能
   - 支持模擬和實盤交易
   - 可設置股票代碼、價格、數量進行買賣
   - 支持訂單管理和撤單操作

3. K線圖表
   - 查看股票的歷史K線數據
   - 支持多種時間週期
   - 自動保存數據到本地

4. 實時監控
   - 監控股票實時價格變動
   - 支持多股票同時監控
   - 可設置價格提醒

5. 數據管理
   - 自動保存交易記錄和K線數據
   - 支持數據導出和備份
   - 提供日誌記錄功能

如需更多幫助，請訪問：https://openapi.futunn.com
        """
        
        # 創建幫助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用說明")
        help_window.geometry("600x500")
        help_window.resizable(False, False)
        
        # 創建文本框
        text_frame = ttk.Frame(help_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("微軟雅黑", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
    
    def show_about(self):
        """顯示關於信息"""
        about_text = """
富途量化交易系統 v2.0

基於富途OpenAPI開發的量化交易平台
提供行情數據獲取、交易執行、數據分析等功能

開發框架：
- Python + Tkinter GUI
- 富途OpenAPI
- Pandas數據處理
- Matplotlib圖表

版權所有 © 2025
        """
        messagebox.showinfo("關於", about_text)
    
    def on_closing(self):
        """處理窗口關閉事件"""
        try:
            # 斷開富途連接
            self.futu_client.disconnect()
            
            # 保存配置
            self.config_manager.save_config()
            
            # 記錄關閉日誌
            self.data_manager.save_log("INFO", "系統正常關閉", "system")
            
        except Exception as e:
            print(f"關閉時出錯: {e}")
        finally:
            self.root.destroy()
            sys.exit(0)
    
    def run(self):
        """運行主程序"""
        self.root.mainloop()
