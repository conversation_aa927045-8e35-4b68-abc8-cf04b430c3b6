"""
衍生品交易面板
專門處理認購證/認沽證的價格映射和交易
"""
import tkinter as tk
from tkinter import ttk, messagebox
from futu import *
import threading
from datetime import datetime
import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.derivative_mapper import DerivativeMapper

class DerivativePanel:
    """衍生品交易面板類"""
    
    def __init__(self, parent, futu_client, config_manager, data_manager):
        """
        初始化衍生品面板
        
        Args:
            parent: 父容器
            futu_client: 富途客戶端
            config_manager: 配置管理器
            data_manager: 數據管理器
        """
        self.parent = parent
        self.futu_client = futu_client
        self.config_manager = config_manager
        self.data_manager = data_manager
        
        # 創建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 初始化衍生品映射器
        self.derivative_mapper = DerivativeMapper()
        
        # 變量
        self.is_connected = False
        self.underlying_symbol_var = tk.StringVar(value="hk50.cash")
        self.underlying_price_var = tk.StringVar(value="40000")
        self.selected_mapping_var = tk.StringVar()
        self.derivative_code_var = tk.StringVar()
        self.calculated_price_var = tk.StringVar(value="0.000")
        self.use_black_scholes_var = tk.BooleanVar(value=True)
        self.auto_update_var = tk.BooleanVar(value=False)
        
        # 創建界面
        self.create_widgets()
        
        # 加載映射列表
        self.refresh_mappings()
    
    def create_widgets(self):
        """創建界面組件"""
        # 創建原商品輸入區域
        self.create_underlying_section()
        
        # 創建映射配置區域
        self.create_mapping_section()
        
        # 創建價格計算區域
        self.create_calculation_section()
        
        # 創建交易區域
        self.create_trading_section()
        
        # 創建映射管理區域
        self.create_management_section()
    
    def create_underlying_section(self):
        """創建原商品輸入區域"""
        underlying_frame = ttk.LabelFrame(self.frame, text="原商品信息", padding="10")
        underlying_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：原商品代碼和價格
        row1 = ttk.Frame(underlying_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="原商品代碼:").pack(side=tk.LEFT)
        underlying_entry = ttk.Entry(row1, textvariable=self.underlying_symbol_var, width=15)
        underlying_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="當前價格:").pack(side=tk.LEFT)
        price_entry = ttk.Entry(row1, textvariable=self.underlying_price_var, width=12)
        price_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        # 自動更新選項
        ttk.Checkbutton(
            row1, text="自動更新", variable=self.auto_update_var,
            command=self.toggle_auto_update
        ).pack(side=tk.LEFT, padx=(20, 0))
        
        # 第二行：操作按鈕
        row2 = ttk.Frame(underlying_frame)
        row2.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(row2, text="獲取實時價格", command=self.fetch_underlying_price).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(row2, text="計算衍生品價格", command=self.calculate_derivative_price).pack(side=tk.LEFT, padx=(0, 10))
    
    def create_mapping_section(self):
        """創建映射配置區域"""
        mapping_frame = ttk.LabelFrame(self.frame, text="衍生品映射", padding="10")
        mapping_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 映射選擇
        row1 = ttk.Frame(mapping_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="選擇映射:").pack(side=tk.LEFT)
        self.mapping_combo = ttk.Combobox(row1, textvariable=self.selected_mapping_var, width=25, state="readonly")
        self.mapping_combo.pack(side=tk.LEFT, padx=(5, 20))
        self.mapping_combo.bind('<<ComboboxSelected>>', self.on_mapping_selected)
        
        ttk.Button(row1, text="刷新", command=self.refresh_mappings).pack(side=tk.LEFT, padx=(0, 10))
        
        # 衍生品代碼顯示
        row2 = ttk.Frame(mapping_frame)
        row2.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(row2, text="衍生品代碼:").pack(side=tk.LEFT)
        ttk.Entry(row2, textvariable=self.derivative_code_var, width=15, state="readonly").pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row2, text="計算價格:").pack(side=tk.LEFT)
        ttk.Entry(row2, textvariable=self.calculated_price_var, width=12, state="readonly").pack(side=tk.LEFT, padx=(5, 0))
    
    def create_calculation_section(self):
        """創建價格計算區域"""
        calc_frame = ttk.LabelFrame(self.frame, text="價格計算設置", padding="10")
        calc_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 計算方法選擇
        ttk.Radiobutton(
            calc_frame, text="Black-Scholes模型", variable=self.use_black_scholes_var, 
            value=True, command=self.calculate_derivative_price
        ).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Radiobutton(
            calc_frame, text="簡單線性映射", variable=self.use_black_scholes_var, 
            value=False, command=self.calculate_derivative_price
        ).pack(side=tk.LEFT)
        
        # 價格範圍顯示
        self.price_range_var = tk.StringVar(value="1小時價格範圍: 未計算")
        ttk.Label(calc_frame, textvariable=self.price_range_var).pack(side=tk.RIGHT)
    
    def create_trading_section(self):
        """創建交易區域"""
        trading_frame = ttk.LabelFrame(self.frame, text="衍生品交易", padding="10")
        trading_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 交易參數
        row1 = ttk.Frame(trading_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="交易數量:").pack(side=tk.LEFT)
        self.qty_var = tk.StringVar(value="1000")
        ttk.Entry(row1, textvariable=self.qty_var, width=10).pack(side=tk.LEFT, padx=(5, 20))
        
        # 交易模式
        self.trade_mode_var = tk.StringVar(value="simulate")
        ttk.Radiobutton(row1, text="模擬", variable=self.trade_mode_var, value="simulate").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(row1, text="實盤", variable=self.trade_mode_var, value="real").pack(side=tk.LEFT)
        
        # 交易按鈕
        row2 = ttk.Frame(trading_frame)
        row2.pack(fill=tk.X, pady=(10, 0))
        
        self.buy_btn = ttk.Button(
            row2, text="買入衍生品", command=lambda: self.place_derivative_order("BUY"),
            state=tk.DISABLED
        )
        self.buy_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.sell_btn = ttk.Button(
            row2, text="賣出衍生品", command=lambda: self.place_derivative_order("SELL"),
            state=tk.DISABLED
        )
        self.sell_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 狀態顯示
        self.status_var = tk.StringVar(value="未連接")
        ttk.Label(row2, textvariable=self.status_var).pack(side=tk.RIGHT)
    
    def create_management_section(self):
        """創建映射管理區域"""
        mgmt_frame = ttk.LabelFrame(self.frame, text="映射管理", padding="10")
        mgmt_frame.pack(fill=tk.BOTH, expand=True)
        
        # 按鈕區域
        btn_frame = ttk.Frame(mgmt_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(btn_frame, text="新增映射", command=self.add_new_mapping).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="編輯映射", command=self.edit_mapping).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="刪除映射", command=self.delete_mapping).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="導入配置", command=self.import_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="導出配置", command=self.export_config).pack(side=tk.LEFT)
        
        # 映射詳情顯示
        self.detail_text = tk.Text(mgmt_frame, height=8, state=tk.DISABLED)
        detail_scrollbar = ttk.Scrollbar(mgmt_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def refresh_mappings(self):
        """刷新映射列表"""
        try:
            mappings = self.derivative_mapper.get_all_mappings()
            mapping_names = []
            
            for mapping_id, mapping in mappings.items():
                name = f"{mapping.get('name', mapping_id)} ({mapping_id})"
                mapping_names.append(name)
            
            self.mapping_combo['values'] = mapping_names
            
            if mapping_names and not self.selected_mapping_var.get():
                self.mapping_combo.current(0)
                self.on_mapping_selected()
                
        except Exception as e:
            messagebox.showerror("錯誤", f"刷新映射列表失敗: {str(e)}")
    
    def on_mapping_selected(self, event=None):
        """映射選擇改變時的處理"""
        try:
            selected = self.selected_mapping_var.get()
            if not selected:
                return
            
            # 提取映射ID
            mapping_id = selected.split('(')[-1].rstrip(')')
            
            # 獲取映射配置
            mappings = self.derivative_mapper.get_all_mappings()
            if mapping_id in mappings:
                mapping = mappings[mapping_id]
                
                # 更新界面
                self.derivative_code_var.set(mapping.get('derivative_code', ''))
                self.underlying_symbol_var.set(mapping.get('underlying_symbol', ''))
                
                # 顯示映射詳情
                self.show_mapping_details(mapping_id, mapping)
                
                # 重新計算價格
                self.calculate_derivative_price()
                
        except Exception as e:
            print(f"處理映射選擇失敗: {e}")
    
    def show_mapping_details(self, mapping_id: str, mapping: dict):
        """顯示映射詳情"""
        try:
            self.detail_text.config(state=tk.NORMAL)
            self.detail_text.delete(1.0, tk.END)
            
            details = f"""映射ID: {mapping_id}
名稱: {mapping.get('name', '未設置')}
原商品: {mapping.get('underlying_symbol', '未設置')}
衍生品代碼: {mapping.get('derivative_code', '未設置')}
期權類型: {mapping.get('option_type', '未設置')}
行權價: {mapping.get('strike_price', 0)}
到期日: {mapping.get('expiry_date', '未設置')}
Delta: {mapping.get('delta', 0)}
波動率: {mapping.get('volatility', 0)}
價格倍數: {mapping.get('price_multiplier', 1)}
價格範圍: {mapping.get('min_price', 0)} - {mapping.get('max_price', 0)}
狀態: {'啟用' if mapping.get('enabled', True) else '禁用'}"""
            
            self.detail_text.insert(tk.END, details)
            self.detail_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"顯示映射詳情失敗: {e}")
    
    def fetch_underlying_price(self):
        """獲取原商品實時價格"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        # 這裡需要根據實際情況實現價格獲取
        # 由於原商品可能不是富途的標準代碼，可能需要特殊處理
        messagebox.showinfo("提示", "實時價格獲取功能需要根據具體的原商品來源進行實現")
    
    def calculate_derivative_price(self):
        """計算衍生品價格"""
        try:
            underlying_price = float(self.underlying_price_var.get())
            underlying_symbol = self.underlying_symbol_var.get()
            
            selected = self.selected_mapping_var.get()
            if not selected:
                messagebox.showwarning("警告", "請先選擇映射配置")
                return
            
            mapping_id = selected.split('(')[-1].rstrip(')')
            use_bs = self.use_black_scholes_var.get()
            
            # 執行價格映射
            result = self.derivative_mapper.map_price(
                underlying_symbol, underlying_price, mapping_id, use_bs
            )
            
            if result:
                self.calculated_price_var.set(f"{result['derivative_price']:.3f}")
                
                # 更新價格範圍顯示
                price_range = result['price_range_1h']
                if price_range['low'] and price_range['high']:
                    range_text = f"1小時價格範圍: {price_range['low']:.0f} - {price_range['high']:.0f}"
                else:
                    range_text = "1小時價格範圍: 數據不足"
                self.price_range_var.set(range_text)
                
                self.status_var.set(f"價格計算完成 ({result['calculation_method']})")
            else:
                messagebox.showerror("錯誤", "價格計算失敗")
                
        except ValueError:
            messagebox.showerror("錯誤", "請輸入正確的價格數值")
        except Exception as e:
            messagebox.showerror("錯誤", f"計算衍生品價格失敗: {str(e)}")
    
    def place_derivative_order(self, direction: str):
        """下衍生品訂單"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        try:
            code = self.derivative_code_var.get()
            price = float(self.calculated_price_var.get())
            qty = int(self.qty_var.get())
            
            if not code or price <= 0 or qty <= 0:
                messagebox.showerror("錯誤", "請檢查交易參數")
                return
            
            # 確認實盤交易
            is_real = self.trade_mode_var.get() == "real"
            if is_real:
                if not messagebox.askyesno("確認", f"確定要在實盤{direction}衍生品嗎？\n代碼：{code}\n價格：{price}\n數量：{qty}"):
                    return
            
            def execute_order():
                try:
                    trd_env = TrdEnv.REAL if is_real else TrdEnv.SIMULATE
                    trd_side = TrdSide.BUY if direction == "BUY" else TrdSide.SELL
                    
                    ret, data = self.futu_client.place_order(
                        price=price, qty=qty, code=code, trd_side=trd_side, trd_env=trd_env
                    )
                    
                    if ret == RET_OK:
                        messagebox.showinfo("成功", f"衍生品下單成功！\n訂單ID: {data.iloc[0]['order_id']}")
                        
                        # 保存交易記錄
                        trade_record = {
                            "type": "derivative",
                            "underlying_symbol": self.underlying_symbol_var.get(),
                            "underlying_price": self.underlying_price_var.get(),
                            "derivative_code": code,
                            "direction": direction,
                            "price": price,
                            "qty": qty,
                            "environment": "real" if is_real else "simulate",
                            "mapping_id": self.selected_mapping_var.get(),
                            "order_id": data.iloc[0]['order_id']
                        }
                        self.data_manager.save_trade_record(trade_record)
                        
                        self.status_var.set("衍生品下單成功")
                    else:
                        messagebox.showerror("錯誤", f"衍生品下單失敗: {data}")
                        
                except Exception as e:
                    messagebox.showerror("錯誤", f"衍生品下單異常: {str(e)}")
            
            threading.Thread(target=execute_order, daemon=True).start()
            
        except ValueError:
            messagebox.showerror("錯誤", "請檢查價格和數量格式")
    
    def toggle_auto_update(self):
        """切換自動更新狀態"""
        # TODO: 實現自動更新邏輯
        if self.auto_update_var.get():
            self.status_var.set("自動更新已啟用")
        else:
            self.status_var.set("自動更新已禁用")
    
    def add_new_mapping(self):
        """添加新映射"""
        # TODO: 實現新增映射對話框
        messagebox.showinfo("提示", "新增映射功能開發中...")
    
    def edit_mapping(self):
        """編輯映射"""
        # TODO: 實現編輯映射對話框
        messagebox.showinfo("提示", "編輯映射功能開發中...")
    
    def delete_mapping(self):
        """刪除映射"""
        # TODO: 實現刪除映射功能
        messagebox.showinfo("提示", "刪除映射功能開發中...")
    
    def import_config(self):
        """導入配置"""
        # TODO: 實現配置導入功能
        messagebox.showinfo("提示", "導入配置功能開發中...")
    
    def export_config(self):
        """導出配置"""
        # TODO: 實現配置導出功能
        messagebox.showinfo("提示", "導出配置功能開發中...")
    
    def on_connection_changed(self, connected: bool):
        """連接狀態改變時的處理"""
        self.is_connected = connected
        
        # 更新按鈕狀態
        state = tk.NORMAL if connected else tk.DISABLED
        self.buy_btn.config(state=state)
        self.sell_btn.config(state=state)
        
        if connected:
            self.status_var.set("已連接，可以進行衍生品交易")
        else:
            self.status_var.set("未連接")
    
    def handle_external_signal(self, underlying_symbol: str, underlying_price: float):
        """處理外部價格信號"""
        try:
            # 更新原商品價格
            self.underlying_symbol_var.set(underlying_symbol)
            self.underlying_price_var.set(str(underlying_price))
            
            # 如果啟用自動更新，則重新計算價格
            if self.auto_update_var.get():
                self.calculate_derivative_price()
                
        except Exception as e:
            print(f"處理外部信號失敗: {e}")
