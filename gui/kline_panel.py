"""
K線圖表面板
提供K線數據查看和圖表顯示功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
from futu import *
import pandas as pd
import matplotlib.pyplot as plt
import mplfinance as mpf
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import threading
from datetime import datetime

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class KLinePanel:
    """K線圖表面板類"""
    
    def __init__(self, parent, futu_client, config_manager, data_manager):
        """
        初始化K線面板
        
        Args:
            parent: 父容器
            futu_client: 富途客戶端
            config_manager: 配置管理器
            data_manager: 數據管理器
        """
        self.parent = parent
        self.futu_client = futu_client
        self.config_manager = config_manager
        self.data_manager = data_manager
        
        # 創建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 變量
        self.code_var = tk.StringVar(value="HK.00700")
        self.period_var = tk.StringVar(value="日K")
        self.count_var = tk.StringVar(value="100")
        self.is_connected = False
        
        # 當前數據
        self.current_data = None
        
        # 創建界面
        self.create_widgets()
    
    def create_widgets(self):
        """創建界面組件"""
        # 創建控制區域
        self.create_control_section()
        
        # 創建圖表區域
        self.create_chart_section()
        
        # 創建數據表格區域
        self.create_table_section()
    
    def create_control_section(self):
        """創建控制區域"""
        control_frame = ttk.LabelFrame(self.frame, text="K線參數", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：股票代碼和週期
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="股票代碼:").pack(side=tk.LEFT)
        code_entry = ttk.Entry(row1, textvariable=self.code_var, width=15)
        code_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="時間週期:").pack(side=tk.LEFT)
        period_combo = ttk.Combobox(row1, textvariable=self.period_var, width=10, state="readonly")
        period_combo['values'] = ("1分鐘", "5分鐘", "15分鐘", "30分鐘", "60分鐘", "日K", "週K", "月K")
        period_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="數據量:").pack(side=tk.LEFT)
        count_entry = ttk.Entry(row1, textvariable=self.count_var, width=10)
        count_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第二行：操作按鈕
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=(10, 0))
        
        self.fetch_btn = ttk.Button(
            row2, text="獲取K線", command=self.fetch_kline_data, state=tk.DISABLED
        )
        self.fetch_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(row2, text="保存數據", command=self.save_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(row2, text="加載數據", command=self.load_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(row2, text="導出圖表", command=self.export_chart).pack(side=tk.LEFT, padx=(0, 10))
        
        # 狀態顯示
        self.status_var = tk.StringVar(value="未連接")
        ttk.Label(row2, textvariable=self.status_var).pack(side=tk.RIGHT)
    
    def create_chart_section(self):
        """創建圖表區域"""
        chart_frame = ttk.LabelFrame(self.frame, text="K線圖表", padding="5")
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 創建matplotlib圖表
        self.fig = Figure(figsize=(12, 6), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具欄
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 初始化空圖表
        self.ax = self.fig.add_subplot(111)
        self.ax.text(0.5, 0.5, '請先獲取K線數據', ha='center', va='center', transform=self.ax.transAxes)
        self.canvas.draw()
    
    def create_table_section(self):
        """創建數據表格區域"""
        table_frame = ttk.LabelFrame(self.frame, text="K線數據", padding="5")
        table_frame.pack(fill=tk.X)
        
        # 創建表格
        columns = ("時間", "開盤", "最高", "最低", "收盤", "成交量", "成交額")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)
        
        # 滾動條
        scrollbar_v = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
    
    def get_kline_type(self):
        """獲取K線類型"""
        period_map = {
            "1分鐘": KLType.K_1M,
            "5分鐘": KLType.K_5M,
            "15分鐘": KLType.K_15M,
            "30分鐘": KLType.K_30M,
            "60分鐘": KLType.K_60M,
            "日K": KLType.K_DAY,
            "週K": KLType.K_WEEK,
            "月K": KLType.K_MON
        }
        return period_map.get(self.period_var.get(), KLType.K_DAY)
    
    def fetch_kline_data(self):
        """獲取K線數據"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        code = self.code_var.get().strip()
        if not code:
            messagebox.showerror("錯誤", "請輸入股票代碼")
            return
        
        try:
            count = min(int(self.count_var.get()), 1000)
        except ValueError:
            messagebox.showerror("錯誤", "請輸入正確的數據量")
            return
        
        def fetch_data():
            try:
                self.status_var.set("正在獲取數據...")
                self.fetch_btn.config(state=tk.DISABLED)
                
                period = self.get_kline_type()
                ret, data = self.futu_client.get_history_kline(
                    code=code, ktype=period, max_count=count
                )
                
                if ret == RET_OK:
                    self.current_data = data
                    
                    # 更新表格
                    self.update_table(data)
                    
                    # 更新圖表
                    self.update_chart(data)
                    
                    # 自動保存數據
                    if self.config_manager.get('data.auto_save_kline', True):
                        filename = self.data_manager.save_kline_data(
                            code, data, self.period_var.get()
                        )
                        if filename:
                            self.status_var.set(f"數據已獲取並保存")
                        else:
                            self.status_var.set("數據已獲取")
                    else:
                        self.status_var.set("數據已獲取")
                        
                else:
                    messagebox.showerror("錯誤", f"獲取數據失敗: {data}")
                    self.status_var.set("獲取失敗")
                    
            except Exception as e:
                messagebox.showerror("錯誤", f"獲取數據異常: {str(e)}")
                self.status_var.set("發生錯誤")
            finally:
                self.fetch_btn.config(state=tk.NORMAL)
        
        threading.Thread(target=fetch_data, daemon=True).start()
    
    def update_table(self, data):
        """更新數據表格"""
        # 清空現有數據
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新數據（顯示最新的20條）
        display_data = data.tail(20)
        for _, row in display_data.iterrows():
            self.tree.insert("", tk.END, values=(
                row.get('time_key', ''),
                f"{row.get('open', 0):.3f}",
                f"{row.get('high', 0):.3f}",
                f"{row.get('low', 0):.3f}",
                f"{row.get('close', 0):.3f}",
                f"{row.get('volume', 0):,.0f}",
                f"{row.get('turnover', 0):,.0f}"
            ))
    
    def update_chart(self, data):
        """更新K線圖表"""
        try:
            # 清空圖表
            self.fig.clear()
            
            # 準備數據
            chart_data = data.copy()
            chart_data.set_index('time_key', inplace=True)
            chart_data.index = pd.to_datetime(chart_data.index)
            
            # 確保數據列名正確
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in chart_data.columns for col in required_columns):
                raise ValueError("數據格式不正確")
            
            # 創建K線圖
            mpf.plot(
                chart_data,
                type='candle',
                style='charles',
                title=f"{self.code_var.get()} K線圖",
                ylabel='價格',
                volume=True,
                fig=self.fig,
                show_nontrading=False
            )
            
            # 更新畫布
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新圖表失敗: {e}")
            # 顯示錯誤信息
            self.fig.clear()
            ax = self.fig.add_subplot(111)
            ax.text(0.5, 0.5, f'圖表更新失敗: {str(e)}', ha='center', va='center', transform=ax.transAxes)
            self.canvas.draw()
    
    def save_data(self):
        """保存當前數據"""
        if self.current_data is None:
            messagebox.showwarning("警告", "沒有可保存的數據")
            return
        
        try:
            filename = self.data_manager.save_kline_data(
                self.code_var.get(), self.current_data, self.period_var.get()
            )
            if filename:
                messagebox.showinfo("成功", f"數據已保存到: {filename}")
            else:
                messagebox.showerror("錯誤", "保存數據失敗")
        except Exception as e:
            messagebox.showerror("錯誤", f"保存數據異常: {str(e)}")
    
    def load_data(self):
        """加載歷史數據"""
        try:
            data = self.data_manager.load_kline_data(
                self.code_var.get(), self.period_var.get()
            )
            
            if data is not None:
                self.current_data = data
                self.update_table(data)
                self.update_chart(data)
                self.status_var.set("歷史數據已加載")
                messagebox.showinfo("成功", "歷史數據加載成功")
            else:
                messagebox.showwarning("警告", "沒有找到歷史數據")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"加載數據異常: {str(e)}")
    
    def export_chart(self):
        """導出圖表"""
        if self.current_data is None:
            messagebox.showwarning("警告", "沒有可導出的圖表")
            return
        
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("JPG files", "*.jpg"), ("All files", "*.*")]
            )
            
            if filename:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"圖表已導出到: {filename}")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"導出圖表異常: {str(e)}")
    
    def on_connection_changed(self, connected: bool):
        """連接狀態改變時的處理"""
        self.is_connected = connected
        
        # 更新按鈕狀態
        state = tk.NORMAL if connected else tk.DISABLED
        self.fetch_btn.config(state=state)
        
        if connected:
            self.status_var.set("已連接，可以獲取數據")
        else:
            self.status_var.set("未連接")
