"""
實時監控面板
提供股票實時監控和價格提醒功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
from futu import *
import threading
import time
from datetime import datetime
from typing import Dict, List

class MonitoringPanel:
    """實時監控面板類"""
    
    def __init__(self, parent, futu_client, config_manager, data_manager):
        """
        初始化監控面板
        
        Args:
            parent: 父容器
            futu_client: 富途客戶端
            config_manager: 配置管理器
            data_manager: 數據管理器
        """
        self.parent = parent
        self.futu_client = futu_client
        self.config_manager = config_manager
        self.data_manager = data_manager
        
        # 創建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 監控變量
        self.is_connected = False
        self.is_monitoring = False
        self.monitor_thread = None
        self.monitored_stocks = {}  # {code: {price_alert, ...}}
        
        # 界面變量
        self.code_var = tk.StringVar(value="HK.00700")
        self.alert_price_var = tk.StringVar(value="")
        self.refresh_interval_var = tk.StringVar(value="1")
        
        # 創建界面
        self.create_widgets()
    
    def create_widgets(self):
        """創建界面組件"""
        # 創建控制區域
        self.create_control_section()
        
        # 創建監控列表
        self.create_monitor_list()
        
        # 創建實時數據顯示
        self.create_realtime_display()
    
    def create_control_section(self):
        """創建控制區域"""
        control_frame = ttk.LabelFrame(self.frame, text="監控控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：添加監控股票
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="股票代碼:").pack(side=tk.LEFT)
        code_entry = ttk.Entry(row1, textvariable=self.code_var, width=15)
        code_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="提醒價格:").pack(side=tk.LEFT)
        alert_entry = ttk.Entry(row1, textvariable=self.alert_price_var, width=10)
        alert_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Button(row1, text="添加監控", command=self.add_monitor).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(row1, text="移除監控", command=self.remove_monitor).pack(side=tk.LEFT)
        
        # 第二行：監控控制
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=(10, 0))
        
        self.start_btn = ttk.Button(
            row2, text="開始監控", command=self.start_monitoring, state=tk.DISABLED
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(
            row2, text="停止監控", command=self.stop_monitoring, state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row2, text="刷新間隔(秒):").pack(side=tk.LEFT, padx=(20, 5))
        interval_entry = ttk.Entry(row2, textvariable=self.refresh_interval_var, width=5)
        interval_entry.pack(side=tk.LEFT)
        
        # 狀態顯示
        self.status_var = tk.StringVar(value="未連接")
        ttk.Label(row2, textvariable=self.status_var).pack(side=tk.RIGHT)
    
    def create_monitor_list(self):
        """創建監控列表"""
        list_frame = ttk.LabelFrame(self.frame, text="監控列表", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 創建表格
        columns = ("股票代碼", "當前價格", "提醒價格", "漲跌", "漲跌%", "成交量", "更新時間")
        self.monitor_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.monitor_tree.heading(col, text=col)
            self.monitor_tree.column(col, width=100)
        
        # 滾動條
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.monitor_tree.yview)
        self.monitor_tree.configure(yscrollcommand=scrollbar.set)
        
        self.monitor_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 綁定雙擊事件
        self.monitor_tree.bind("<Double-1>", self.on_item_double_click)
    
    def create_realtime_display(self):
        """創建實時數據顯示"""
        display_frame = ttk.LabelFrame(self.frame, text="實時信息", padding="5")
        display_frame.pack(fill=tk.X)
        
        # 創建文本框顯示實時信息
        self.info_text = tk.Text(display_frame, height=6, state=tk.DISABLED)
        info_scrollbar = ttk.Scrollbar(display_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def add_monitor(self):
        """添加監控股票"""
        code = self.code_var.get().strip()
        if not code:
            messagebox.showerror("錯誤", "請輸入股票代碼")
            return
        
        if code in self.monitored_stocks:
            messagebox.showwarning("警告", "該股票已在監控列表中")
            return
        
        try:
            alert_price = float(self.alert_price_var.get()) if self.alert_price_var.get() else None
        except ValueError:
            alert_price = None
        
        # 添加到監控列表
        self.monitored_stocks[code] = {
            'alert_price': alert_price,
            'last_price': None,
            'last_update': None
        }
        
        # 更新表格
        self.monitor_tree.insert("", tk.END, values=(
            code, "獲取中...", alert_price or "無", "-", "-", "-", "未更新"
        ))
        
        # 清空輸入框
        self.code_var.set("")
        self.alert_price_var.set("")
        
        self.add_info_message(f"已添加監控: {code}")
    
    def remove_monitor(self):
        """移除監控股票"""
        selected = self.monitor_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "請選擇要移除的股票")
            return
        
        for item in selected:
            values = self.monitor_tree.item(item, 'values')
            code = values[0]
            
            # 從監控列表中移除
            if code in self.monitored_stocks:
                del self.monitored_stocks[code]
            
            # 從表格中移除
            self.monitor_tree.delete(item)
            
            self.add_info_message(f"已移除監控: {code}")
    
    def start_monitoring(self):
        """開始監控"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        if not self.monitored_stocks:
            messagebox.showwarning("警告", "請先添加要監控的股票")
            return
        
        if self.is_monitoring:
            return
        
        try:
            interval = float(self.refresh_interval_var.get())
            if interval < 0.5:
                messagebox.showerror("錯誤", "刷新間隔不能小於0.5秒")
                return
        except ValueError:
            messagebox.showerror("錯誤", "請輸入正確的刷新間隔")
            return
        
        self.is_monitoring = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        # 啟動監控線程
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.status_var.set("監控中...")
        self.add_info_message("開始實時監控")
    
    def stop_monitoring(self):
        """停止監控"""
        self.is_monitoring = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        
        self.status_var.set("已停止監控")
        self.add_info_message("停止實時監控")
    
    def monitor_loop(self):
        """監控循環"""
        while self.is_monitoring and self.is_connected:
            try:
                # 獲取所有監控股票的數據
                codes = list(self.monitored_stocks.keys())
                if not codes:
                    break
                
                ret, data = self.futu_client.get_market_snapshot(codes)
                
                if ret == RET_OK and len(data) > 0:
                    self.update_monitor_data(data)
                else:
                    self.add_info_message(f"獲取數據失敗: {data}")
                
                # 等待下次刷新
                interval = float(self.refresh_interval_var.get())
                time.sleep(interval)
                
            except Exception as e:
                self.add_info_message(f"監控異常: {str(e)}")
                time.sleep(1)
        
        # 監控結束
        if self.is_monitoring:
            self.stop_monitoring()
    
    def update_monitor_data(self, data):
        """更新監控數據"""
        current_time = datetime.now().strftime("%H:%M:%S")
        
        for _, row in data.iterrows():
            code = row['code']
            if code not in self.monitored_stocks:
                continue
            
            current_price = row['last_price']
            prev_close = row.get('prev_close_price', current_price)
            volume = row.get('volume', 0)
            
            # 計算漲跌
            change = current_price - prev_close
            change_pct = (change / prev_close * 100) if prev_close > 0 else 0
            
            # 檢查價格提醒
            alert_price = self.monitored_stocks[code]['alert_price']
            if alert_price and self.should_alert(code, current_price, alert_price):
                self.trigger_price_alert(code, current_price, alert_price)
            
            # 更新監控記錄
            self.monitored_stocks[code]['last_price'] = current_price
            self.monitored_stocks[code]['last_update'] = current_time
            
            # 更新表格
            self.update_tree_item(code, current_price, alert_price, change, change_pct, volume, current_time)
    
    def should_alert(self, code, current_price, alert_price):
        """判斷是否應該觸發價格提醒"""
        last_price = self.monitored_stocks[code]['last_price']
        
        # 如果是第一次獲取價格，不觸發提醒
        if last_price is None:
            return False
        
        # 檢查是否穿越提醒價格
        if (last_price < alert_price <= current_price) or (last_price > alert_price >= current_price):
            return True
        
        return False
    
    def trigger_price_alert(self, code, current_price, alert_price):
        """觸發價格提醒"""
        message = f"價格提醒: {code} 當前價格 {current_price:.3f} 已觸及提醒價格 {alert_price:.3f}"
        self.add_info_message(message)
        
        # 可以添加聲音提醒
        if self.config_manager.get('monitoring.enable_sound_alerts', False):
            # TODO: 實現聲音提醒
            pass
        
        # 記錄提醒日誌
        self.data_manager.save_log("ALERT", message, "price_alert")
    
    def update_tree_item(self, code, price, alert_price, change, change_pct, volume, update_time):
        """更新表格項目"""
        # 查找對應的表格項目
        for item in self.monitor_tree.get_children():
            values = self.monitor_tree.item(item, 'values')
            if values[0] == code:
                # 設置顏色
                if change > 0:
                    tags = ('positive',)
                elif change < 0:
                    tags = ('negative',)
                else:
                    tags = ('neutral',)
                
                # 更新數據
                self.monitor_tree.item(item, values=(
                    code,
                    f"{price:.3f}",
                    alert_price or "無",
                    f"{change:+.3f}",
                    f"{change_pct:+.2f}%",
                    f"{volume:,.0f}",
                    update_time
                ), tags=tags)
                break
        
        # 配置標籤顏色
        self.monitor_tree.tag_configure('positive', foreground='red')
        self.monitor_tree.tag_configure('negative', foreground='green')
        self.monitor_tree.tag_configure('neutral', foreground='black')
    
    def add_info_message(self, message):
        """添加信息消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.info_text.config(state=tk.NORMAL)
        self.info_text.insert(tk.END, full_message)
        self.info_text.see(tk.END)
        self.info_text.config(state=tk.DISABLED)
        
        # 限制文本長度
        lines = self.info_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete("1.0", f"{len(lines)-50}.0")
            self.info_text.config(state=tk.DISABLED)
    
    def on_item_double_click(self, event):
        """表格項目雙擊事件"""
        selected = self.monitor_tree.selection()
        if selected:
            values = self.monitor_tree.item(selected[0], 'values')
            code = values[0]
            
            # 顯示詳細信息
            detail_window = tk.Toplevel(self.frame)
            detail_window.title(f"{code} 詳細信息")
            detail_window.geometry("400x300")
            
            # TODO: 實現詳細信息窗口
            ttk.Label(detail_window, text=f"{code} 詳細信息開發中...").pack(pady=20)
    
    def on_connection_changed(self, connected: bool):
        """連接狀態改變時的處理"""
        self.is_connected = connected
        
        if connected:
            self.start_btn.config(state=tk.NORMAL)
            self.status_var.set("已連接，可以開始監控")
        else:
            # 停止監控
            if self.is_monitoring:
                self.stop_monitoring()
            
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.DISABLED)
            self.status_var.set("未連接")
            
            # 清空價格數據
            for item in self.monitor_tree.get_children():
                values = list(self.monitor_tree.item(item, 'values'))
                values[1] = "未連接"  # 當前價格
                values[3] = "-"      # 漲跌
                values[4] = "-"      # 漲跌%
                values[5] = "-"      # 成交量
                values[6] = "未連接"  # 更新時間
                self.monitor_tree.item(item, values=values)
