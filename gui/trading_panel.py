"""
交易面板
提供股票交易功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
from futu import *
import threading
from typing import Optional

class TradingPanel:
    """交易面板類"""
    
    def __init__(self, parent, futu_client, config_manager, data_manager):
        """
        初始化交易面板
        
        Args:
            parent: 父容器
            futu_client: 富途客戶端
            config_manager: 配置管理器
            data_manager: 數據管理器
        """
        self.parent = parent
        self.futu_client = futu_client
        self.config_manager = config_manager
        self.data_manager = data_manager
        
        # 創建主框架
        self.frame = ttk.Frame(parent, padding="10")
        
        # 交易變量
        self.mode_var = tk.StringVar(value="simulate")
        self.code_var = tk.StringVar(value="HK.00700")
        self.price_var = tk.StringVar(value="100.0")
        self.qty_var = tk.StringVar(value="100")
        
        # 狀態變量
        self.is_connected = False
        
        # 創建界面
        self.create_widgets()
    
    def create_widgets(self):
        """創建界面組件"""
        # 交易模式選擇
        self.create_mode_section()
        
        # 交易參數設置
        self.create_trading_section()
        
        # 訂單管理
        self.create_order_section()
        
        # 持倉信息
        self.create_position_section()
    
    def create_mode_section(self):
        """創建交易模式選擇區域"""
        mode_frame = ttk.LabelFrame(self.frame, text="交易模式", padding="10")
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 模擬交易
        ttk.Radiobutton(
            mode_frame, text="模擬交易", variable=self.mode_var, 
            value="simulate", command=self.on_mode_changed
        ).pack(side=tk.LEFT, padx=(0, 20))
        
        # 實盤交易
        ttk.Radiobutton(
            mode_frame, text="實盤交易", variable=self.mode_var, 
            value="real", command=self.on_mode_changed
        ).pack(side=tk.LEFT)
        
        # 狀態提示
        self.mode_status_var = tk.StringVar(value="當前：模擬交易模式")
        ttk.Label(mode_frame, textvariable=self.mode_status_var).pack(side=tk.RIGHT)
    
    def create_trading_section(self):
        """創建交易參數設置區域"""
        trading_frame = ttk.LabelFrame(self.frame, text="交易參數", padding="10")
        trading_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：股票代碼和價格
        row1 = ttk.Frame(trading_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="股票代碼:").pack(side=tk.LEFT)
        code_entry = ttk.Entry(row1, textvariable=self.code_var, width=15)
        code_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="價格:").pack(side=tk.LEFT)
        price_entry = ttk.Entry(row1, textvariable=self.price_var, width=10)
        price_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="數量:").pack(side=tk.LEFT)
        qty_entry = ttk.Entry(row1, textvariable=self.qty_var, width=10)
        qty_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第二行：交易按鈕
        row2 = ttk.Frame(trading_frame)
        row2.pack(fill=tk.X, pady=(10, 0))
        
        self.buy_btn = ttk.Button(
            row2, text="買入", command=lambda: self.place_order("BUY"),
            state=tk.DISABLED
        )
        self.buy_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.sell_btn = ttk.Button(
            row2, text="賣出", command=lambda: self.place_order("SELL"),
            state=tk.DISABLED
        )
        self.sell_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 快速金額按鈕
        ttk.Button(row2, text="1萬", command=lambda: self.set_amount(10000)).pack(side=tk.LEFT, padx=(20, 5))
        ttk.Button(row2, text="5萬", command=lambda: self.set_amount(50000)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="10萬", command=lambda: self.set_amount(100000)).pack(side=tk.LEFT, padx=(0, 5))
        
        # 獲取報價按鈕
        ttk.Button(row2, text="獲取報價", command=self.get_quote).pack(side=tk.RIGHT)
    
    def create_order_section(self):
        """創建訂單管理區域"""
        order_frame = ttk.LabelFrame(self.frame, text="訂單管理", padding="10")
        order_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 按鈕區域
        btn_frame = ttk.Frame(order_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(btn_frame, text="刷新訂單", command=self.refresh_orders).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="撤銷所有", command=self.cancel_all_orders).pack(side=tk.LEFT)
        
        # 訂單列表
        columns = ("訂單ID", "股票代碼", "方向", "價格", "數量", "狀態", "時間")
        self.order_tree = ttk.Treeview(order_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.order_tree.heading(col, text=col)
            self.order_tree.column(col, width=100)
        
        # 滾動條
        order_scrollbar = ttk.Scrollbar(order_frame, orient=tk.VERTICAL, command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=order_scrollbar.set)
        
        self.order_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        order_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_position_section(self):
        """創建持倉信息區域"""
        position_frame = ttk.LabelFrame(self.frame, text="持倉信息", padding="10")
        position_frame.pack(fill=tk.X)
        
        # 刷新按鈕
        ttk.Button(position_frame, text="刷新持倉", command=self.refresh_positions).pack(anchor=tk.W, pady=(0, 10))
        
        # 持倉信息顯示
        self.position_text = tk.Text(position_frame, height=4, state=tk.DISABLED)
        self.position_text.pack(fill=tk.X)
    
    def on_mode_changed(self):
        """交易模式改變時的處理"""
        mode = self.mode_var.get()
        if mode == "simulate":
            self.mode_status_var.set("當前：模擬交易模式")
        else:
            self.mode_status_var.set("當前：實盤交易模式 ⚠️")
        
        # 刷新訂單和持倉
        self.refresh_orders()
        self.refresh_positions()
    
    def set_amount(self, amount: int):
        """設置交易金額"""
        try:
            price = float(self.price_var.get())
            if price > 0:
                qty = int(amount / price)
                self.qty_var.set(str(qty))
        except ValueError:
            messagebox.showerror("錯誤", "請先設置正確的價格")
    
    def get_quote(self):
        """獲取股票報價"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        code = self.code_var.get().strip()
        if not code:
            messagebox.showerror("錯誤", "請輸入股票代碼")
            return
        
        def fetch_quote():
            try:
                ret, data = self.futu_client.get_market_snapshot([code])
                if ret == RET_OK and len(data) > 0:
                    price = data.iloc[0]['last_price']
                    self.price_var.set(f"{price:.3f}")
                    messagebox.showinfo("報價", f"{code} 最新價: {price:.3f}")
                else:
                    messagebox.showerror("錯誤", f"獲取報價失敗: {data}")
            except Exception as e:
                messagebox.showerror("錯誤", f"獲取報價異常: {str(e)}")
        
        threading.Thread(target=fetch_quote, daemon=True).start()
    
    def place_order(self, direction: str):
        """下單"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        try:
            code = self.code_var.get().strip()
            price = float(self.price_var.get())
            qty = int(self.qty_var.get())
            
            if not code or price <= 0 or qty <= 0:
                messagebox.showerror("錯誤", "請檢查交易參數")
                return
            
            # 確認實盤交易
            is_real = self.mode_var.get() == "real"
            if is_real:
                if not messagebox.askyesno("確認", f"確定要在實盤{direction}嗎？\n股票：{code}\n價格：{price}\n數量：{qty}"):
                    return
            
            def execute_order():
                try:
                    trd_env = TrdEnv.REAL if is_real else TrdEnv.SIMULATE
                    trd_side = TrdSide.BUY if direction == "BUY" else TrdSide.SELL
                    
                    ret, data = self.futu_client.place_order(
                        price=price, qty=qty, code=code, trd_side=trd_side, trd_env=trd_env
                    )
                    
                    if ret == RET_OK:
                        messagebox.showinfo("成功", f"下單成功！\n訂單ID: {data.iloc[0]['order_id']}")
                        
                        # 保存交易記錄
                        trade_record = {
                            "code": code,
                            "direction": direction,
                            "price": price,
                            "qty": qty,
                            "environment": "real" if is_real else "simulate",
                            "order_id": data.iloc[0]['order_id']
                        }
                        self.data_manager.save_trade_record(trade_record)
                        
                        # 刷新訂單列表
                        self.refresh_orders()
                    else:
                        messagebox.showerror("錯誤", f"下單失敗: {data}")
                        
                except Exception as e:
                    messagebox.showerror("錯誤", f"下單異常: {str(e)}")
            
            threading.Thread(target=execute_order, daemon=True).start()
            
        except ValueError:
            messagebox.showerror("錯誤", "請檢查價格和數量格式")
    
    def refresh_orders(self):
        """刷新訂單列表"""
        if not self.is_connected:
            return
        
        def fetch_orders():
            try:
                trd_env = TrdEnv.REAL if self.mode_var.get() == "real" else TrdEnv.SIMULATE
                ret, data = self.futu_client.get_order_list(trd_env)
                
                # 清空現有數據
                for item in self.order_tree.get_children():
                    self.order_tree.delete(item)
                
                if ret == RET_OK and len(data) > 0:
                    for _, order in data.iterrows():
                        self.order_tree.insert("", tk.END, values=(
                            order.get('order_id', ''),
                            order.get('code', ''),
                            "買入" if order.get('trd_side') == TrdSide.BUY else "賣出",
                            f"{order.get('price', 0):.3f}",
                            order.get('qty', 0),
                            self.get_order_status_text(order.get('order_status')),
                            order.get('create_time', '')
                        ))
            except Exception as e:
                print(f"刷新訂單失敗: {e}")
        
        threading.Thread(target=fetch_orders, daemon=True).start()
    
    def get_order_status_text(self, status):
        """獲取訂單狀態文本"""
        status_map = {
            OrderStatus.NONE: "無",
            OrderStatus.UNSUBMITTED: "未提交",
            OrderStatus.WAITING_SUBMIT: "等待提交",
            OrderStatus.SUBMITTING: "提交中",
            OrderStatus.SUBMITTED: "已提交",
            OrderStatus.FILLED_PART: "部分成交",
            OrderStatus.FILLED_ALL: "全部成交",
            OrderStatus.CANCELLED_PART: "部分撤單",
            OrderStatus.CANCELLED_ALL: "全部撤單",
            OrderStatus.FAILED: "失敗",
            OrderStatus.DISABLED: "已失效"
        }
        return status_map.get(status, "未知")
    
    def cancel_all_orders(self):
        """撤銷所有訂單"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接富途API")
            return
        
        env_name = "實盤" if self.mode_var.get() == "real" else "模擬"
        if not messagebox.askyesno("確認", f"確定要撤銷所有{env_name}訂單嗎？"):
            return
        
        def cancel_orders():
            try:
                trd_env = TrdEnv.REAL if self.mode_var.get() == "real" else TrdEnv.SIMULATE
                ret, data = self.futu_client.cancel_all_orders(trd_env)
                
                if ret == RET_OK:
                    messagebox.showinfo("成功", "撤單操作完成")
                    self.refresh_orders()
                else:
                    messagebox.showerror("錯誤", f"撤單失敗: {data}")
            except Exception as e:
                messagebox.showerror("錯誤", f"撤單異常: {str(e)}")
        
        threading.Thread(target=cancel_orders, daemon=True).start()
    
    def refresh_positions(self):
        """刷新持倉信息"""
        if not self.is_connected:
            self.position_text.config(state=tk.NORMAL)
            self.position_text.delete(1.0, tk.END)
            self.position_text.insert(tk.END, "未連接到富途API")
            self.position_text.config(state=tk.DISABLED)
            return
        
        def fetch_positions():
            try:
                # TODO: 實現持倉查詢
                self.position_text.config(state=tk.NORMAL)
                self.position_text.delete(1.0, tk.END)
                self.position_text.insert(tk.END, "持倉功能開發中...")
                self.position_text.config(state=tk.DISABLED)
            except Exception as e:
                print(f"刷新持倉失敗: {e}")
        
        threading.Thread(target=fetch_positions, daemon=True).start()
    
    def on_connection_changed(self, connected: bool):
        """連接狀態改變時的處理"""
        self.is_connected = connected
        
        # 更新按鈕狀態
        state = tk.NORMAL if connected else tk.DISABLED
        self.buy_btn.config(state=state)
        self.sell_btn.config(state=state)
        
        if connected:
            self.refresh_orders()
            self.refresh_positions()
        else:
            # 清空數據
            for item in self.order_tree.get_children():
                self.order_tree.delete(item)
            
            self.position_text.config(state=tk.NORMAL)
            self.position_text.delete(1.0, tk.END)
            self.position_text.insert(tk.END, "未連接")
            self.position_text.config(state=tk.DISABLED)
