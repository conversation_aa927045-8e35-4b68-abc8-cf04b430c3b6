#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
富途量化交易系統 v2.0
主程序入口

基於富途OpenAPI開發的量化交易平台
提供行情數據獲取、交易執行、實時監控等功能

使用前請確保：
1. 已安裝FutuOpenD並啟動
2. 已安裝所需的Python依賴包
3. 已配置正確的交易密碼（如需交易功能）

作者: Futu Trading System
版本: 2.0.0
日期: 2025-01-30
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# 添加項目根目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """檢查依賴包"""
    required_packages = [
        ('futu', 'futu-api'),
        ('pandas', 'pandas'),
        ('matplotlib', 'matplotlib'),
        ('mplfinance', 'mplfinance')
    ]
    
    missing_packages = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        error_msg = f"""
缺少必要的依賴包，請先安裝：

pip install {' '.join(missing_packages)}

或者運行：
pip install -r requirements.txt
        """
        messagebox.showerror("依賴包錯誤", error_msg)
        return False
    
    return True

def check_futu_opend():
    """檢查FutuOpenD是否運行"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', 11111))
        sock.close()
        return result == 0
    except:
        return False

def show_startup_info():
    """顯示啟動信息"""
    info_text = """
富途量化交易系統 v2.0 啟動檢查

✓ Python環境正常
✓ 依賴包已安裝
"""
    
    if check_futu_opend():
        info_text += "✓ FutuOpenD已運行\n\n系統準備就緒！"
        title = "啟動成功"
        msg_type = "info"
    else:
        info_text += """✗ FutuOpenD未運行

請先啟動FutuOpenD：
1. 下載並安裝FutuOpenD
2. 登錄您的富途賬號
3. 確保端口11111可用

系統將以離線模式啟動。"""
        title = "啟動警告"
        msg_type = "warning"
    
    if msg_type == "info":
        messagebox.showinfo(title, info_text)
    else:
        messagebox.showwarning(title, info_text)

def main():
    """主函數"""
    try:
        # 檢查依賴
        if not check_dependencies():
            sys.exit(1)
        
        # 顯示啟動信息
        show_startup_info()
        
        # 導入GUI模塊
        from gui.main_window import MainWindow
        
        # 創建並運行主窗口
        app = MainWindow()
        app.run()
        
    except ImportError as e:
        error_msg = f"""
導入模塊失敗: {str(e)}

可能的解決方案：
1. 檢查是否安裝了所有依賴包
2. 檢查Python路徑設置
3. 重新安裝futu-api包

詳細錯誤信息：
{traceback.format_exc()}
        """
        messagebox.showerror("導入錯誤", error_msg)
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"""
程序啟動失敗: {str(e)}

詳細錯誤信息：
{traceback.format_exc()}

請檢查：
1. 系統環境是否正確
2. 配置文件是否有效
3. 權限設置是否正確
        """
        messagebox.showerror("啟動錯誤", error_msg)
        sys.exit(1)

if __name__ == "__main__":
    # 設置異常處理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"""
程序運行時發生未處理的異常：

{exc_type.__name__}: {exc_value}

詳細信息：
{''.join(traceback.format_tb(exc_traceback))}

請將此錯誤信息反饋給開發者。
        """
        
        try:
            messagebox.showerror("程序錯誤", error_msg)
        except:
            print(error_msg)
    
    sys.excepthook = handle_exception
    
    # 運行主程序
    main()
