from futu import *
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_history_data(stock_code, period=KLType.K_DAY, count=100):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        # 獲取歷史K線
        ret, data = quote_ctx.get_history_kline(
            code=stock_code,
            start=None,  # 不指定開始時間，直接用count往前取
            end=None,    # 不指定結束時間，預設取到最新
            ktype=period,
            max_count=count
        )
        
        if ret == RET_OK:
            print(f"\n成功獲取 {stock_code} 的歷史K線數據")
            print(f"數據筆數: {len(data)}")
            
            # 計算一些技術指標
            data['MA5'] = data['close'].rolling(5).mean()
            data['MA10'] = data['close'].rolling(10).mean()
            data['MA20'] = data['close'].rolling(20).mean()
            
            # 顯示最新的幾筆數據
            print("\n最新5筆數據:")
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            print(data.tail().to_string())
            
            # 計算一些統計數據
            print("\n統計數據:")
            print(f"最高價: {data['high'].max():.2f}")
            print(f"最低價: {data['low'].min():.2f}")
            print(f"平均價: {data['close'].mean():.2f}")
            print(f"總成交量: {data['volume'].sum():,.0f}")
            print(f"總成交額: {data['turnover'].sum():,.0f}")
            
            # 計算漲跌幅
            first_close = data.iloc[0]['close']
            last_close = data.iloc[-1]['close']
            change_pct = (last_close - first_close) / first_close * 100
            print(f"\n期間漲跌幅: {change_pct:.2f}%")
            
            # 保存到CSV文件
            filename = f"{stock_code.replace('.', '_')}_{datetime.now().strftime('%Y%m%d')}.csv"
            data.to_csv(filename, index=True)
            print(f"\n數據已保存到文件: {filename}")
            
        else:
            print(f"獲取數據失敗: {data}")
            
    except Exception as e:
        print(f"發生異常: {str(e)}")
        import traceback
        print(traceback.format_exc())
        
    finally:
        quote_ctx.close()

def main():
    # 可以選擇不同的時間週期：
    # K_1M: 1分鐘, K_5M: 5分鐘, K_15M: 15分鐘, K_30M: 30分鐘
    # K_60M: 60分鐘, K_DAY: 日K, K_WEEK: 週K, K_MON: 月K
    
    print("請選擇K線類型：")
    print("1: 1分鐘K線")
    print("2: 5分鐘K線")
    print("3: 15分鐘K線")
    print("4: 30分鐘K線")
    print("5: 60分鐘K線")
    print("6: 日K線")
    print("7: 週K線")
    print("8: 月K線")
    
    choice = input("請輸入選擇(1-8): ")
    period_map = {
        '1': KLType.K_1M,
        '2': KLType.K_5M,
        '3': KLType.K_15M,
        '4': KLType.K_30M,
        '5': KLType.K_60M,
        '6': KLType.K_DAY,
        '7': KLType.K_WEEK,
        '8': KLType.K_MON
    }
    
    period = period_map.get(choice, KLType.K_DAY)
    count = int(input("請輸入要獲取的K線數量(最大1000): "))
    stock_code = input("請輸入股票代碼(例如：HK.00700): ")
    
    get_history_data(stock_code, period, min(count, 1000))

if __name__ == "__main__":
    main() 