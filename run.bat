@echo off
chcp 65001 >nul
title 富途量化交易系統 v2.0

echo.
echo ========================================
echo   富途量化交易系統 v2.0
echo ========================================
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo [錯誤] 未找到Python，請先安裝Python 3.7+
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] Python環境檢查通過

REM 檢查依賴包
echo [信息] 檢查依賴包...
python -c "import futu, pandas, matplotlib, mplfinance" >nul 2>&1
if errorlevel 1 (
    echo [警告] 缺少必要的依賴包，正在安裝...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [錯誤] 依賴包安裝失敗
        pause
        exit /b 1
    )
)

echo [信息] 依賴包檢查通過

REM 檢查FutuOpenD
echo [信息] 檢查FutuOpenD連接...
python -c "import socket; s=socket.socket(); s.settimeout(1); s.connect(('127.0.0.1', 11111)); s.close()" >nul 2>&1
if errorlevel 1 (
    echo [警告] 無法連接到FutuOpenD (127.0.0.1:11111)
    echo [提示] 請確保FutuOpenD已啟動並登錄
    echo [提示] 系統將以離線模式啟動
) else (
    echo [信息] FutuOpenD連接正常
)

echo.
echo [信息] 啟動富途量化交易系統...
echo.

REM 啟動主程序
python main.py

if errorlevel 1 (
    echo.
    echo [錯誤] 程序啟動失敗
    pause
)

echo.
echo [信息] 程序已退出
pause
