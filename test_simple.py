#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試程序
測試基本功能是否正常
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox

# 添加項目根目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """測試導入"""
    try:
        print("測試基本導入...")
        import tkinter as tk
        print("✓ tkinter 導入成功")

        import futu
        print("✓ futu-api 導入成功")

        import pandas as pd
        print("✓ pandas 導入成功")

        import matplotlib.pyplot as plt
        print("✓ matplotlib 導入成功")

        try:
            import mplfinance as mpf
            print("✓ mplfinance 導入成功")
        except ImportError:
            print("⚠ mplfinance 導入失敗，但不影響基本功能")

        print("\n測試核心模塊...")
        from core import ConfigManager, FutuClient, DataManager
        print("✓ 核心模塊導入成功")

        return True

    except Exception as e:
        print(f"✗ 導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """測試配置管理"""
    try:
        print("\n測試配置管理...")
        from core import ConfigManager
        
        config = ConfigManager()
        print("✓ 配置管理器創建成功")
        
        # 測試配置讀取
        host = config.get('futu_api.host', '127.0.0.1')
        print(f"✓ 配置讀取成功: host = {host}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置測試失敗: {e}")
        return False

def test_data_manager():
    """測試數據管理"""
    try:
        print("\n測試數據管理...")
        from core import DataManager
        
        data_mgr = DataManager()
        print("✓ 數據管理器創建成功")
        
        # 測試日誌保存
        data_mgr.save_log("INFO", "測試日誌", "test")
        print("✓ 日誌保存成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 數據管理測試失敗: {e}")
        return False

def test_futu_client():
    """測試富途客戶端"""
    try:
        print("\n測試富途客戶端...")
        from core import FutuClient
        
        config = {'host': '127.0.0.1', 'port': 11111, 'trade_password': ''}
        client = FutuClient(config)
        print("✓ 富途客戶端創建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 富途客戶端測試失敗: {e}")
        return False

def create_simple_gui():
    """創建簡單的GUI測試"""
    try:
        print("\n創建簡單GUI測試...")
        
        root = tk.Tk()
        root.title("富途交易系統 - 簡單測試")
        root.geometry("600x400")
        
        # 創建主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 標題
        title_label = ttk.Label(main_frame, text="富途量化交易系統 v2.0", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 測試結果顯示
        result_text = tk.Text(main_frame, height=15, width=70)
        result_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 運行測試
        test_results = []
        
        if test_imports():
            test_results.append("✓ 模塊導入測試通過")
        else:
            test_results.append("✗ 模塊導入測試失敗")
        
        if test_config():
            test_results.append("✓ 配置管理測試通過")
        else:
            test_results.append("✗ 配置管理測試失敗")
        
        if test_data_manager():
            test_results.append("✓ 數據管理測試通過")
        else:
            test_results.append("✗ 數據管理測試失敗")
        
        if test_futu_client():
            test_results.append("✓ 富途客戶端測試通過")
        else:
            test_results.append("✗ 富途客戶端測試失敗")
        
        # 顯示測試結果
        result_text.insert(tk.END, "系統測試結果:\n\n")
        for result in test_results:
            result_text.insert(tk.END, result + "\n")
        
        result_text.insert(tk.END, "\n" + "="*50 + "\n")
        result_text.insert(tk.END, "如果所有測試都通過，可以嘗試運行完整系統:\n")
        result_text.insert(tk.END, "python main.py\n\n")
        result_text.insert(tk.END, "如果有測試失敗，請檢查相應的依賴包安裝。")
        
        result_text.config(state=tk.DISABLED)
        
        # 按鈕
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)
        
        def run_main_system():
            try:
                root.destroy()
                from gui.main_window import MainWindow
                app = MainWindow()
                app.run()
            except Exception as e:
                messagebox.showerror("錯誤", f"啟動主系統失敗: {str(e)}")
        
        ttk.Button(btn_frame, text="運行完整系統", command=run_main_system).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="退出", command=root.quit).pack(side=tk.RIGHT)
        
        print("✓ 簡單GUI創建成功")
        
        # 運行GUI
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("富途量化交易系統 v2.0 - 簡單測試")
    print("="*50)
    
    # 運行測試
    create_simple_gui()

if __name__ == "__main__":
    main()
