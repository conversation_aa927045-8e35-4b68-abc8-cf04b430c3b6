#!/bin/bash

# 富途量化交易系統 v2.0 啟動腳本 (Linux/macOS)

echo "========================================"
echo "  富途量化交易系統 v2.0"
echo "========================================"
echo

# 檢查Python是否安裝
if ! command -v python3 &> /dev/null; then
    echo "[錯誤] 未找到Python3，請先安裝Python 3.7+"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

echo "[信息] Python環境檢查通過"

# 檢查pip
if ! command -v pip3 &> /dev/null; then
    echo "[錯誤] 未找到pip3，請先安裝pip"
    exit 1
fi

# 檢查依賴包
echo "[信息] 檢查依賴包..."
python3 -c "import futu, pandas, matplotlib, mplfinance" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "[警告] 缺少必要的依賴包，正在安裝..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "[錯誤] 依賴包安裝失敗"
        exit 1
    fi
fi

echo "[信息] 依賴包檢查通過"

# 檢查FutuOpenD
echo "[信息] 檢查FutuOpenD連接..."
python3 -c "import socket; s=socket.socket(); s.settimeout(1); s.connect(('127.0.0.1', 11111)); s.close()" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "[警告] 無法連接到FutuOpenD (127.0.0.1:11111)"
    echo "[提示] 請確保FutuOpenD已啟動並登錄"
    echo "[提示] 系統將以離線模式啟動"
else
    echo "[信息] FutuOpenD連接正常"
fi

echo
echo "[信息] 啟動富途量化交易系統..."
echo

# 啟動主程序
python3 main.py

if [ $? -ne 0 ]; then
    echo
    echo "[錯誤] 程序啟動失敗"
    read -p "按Enter鍵退出..."
fi

echo
echo "[信息] 程序已退出"
