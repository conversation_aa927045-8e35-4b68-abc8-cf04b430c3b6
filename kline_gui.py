from futu import *
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import mplfinance as mpf
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading

class KLineApp:
    def __init__(self, root):
        self.root = root
        self.root.title("富途K線數據查詢")
        self.root.geometry("1200x800")
        
        # 創建左側控制面板
        control_frame = ttk.LabelFrame(root, text="控制面板", padding="10")
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 股票代碼輸入
        ttk.Label(control_frame, text="股票代碼:").pack(anchor=tk.W)
        self.code_var = tk.StringVar(value="HK.00700")
        ttk.Entry(control_frame, textvariable=self.code_var).pack(fill=tk.X, pady=5)
        
        # K線類型選擇
        ttk.Label(control_frame, text="K線類型:").pack(anchor=tk.W)
        self.period_var = tk.StringVar(value="日K線")
        periods = ["1分鐘K線", "5分鐘K線", "15分鐘K線", "30分鐘K線", 
                  "60分鐘K線", "日K線", "週K線", "月K線"]
        ttk.Combobox(control_frame, textvariable=self.period_var, 
                    values=periods, state="readonly").pack(fill=tk.X, pady=5)
        
        # 數據數量輸入
        ttk.Label(control_frame, text="K線數量 (最大1000):").pack(anchor=tk.W)
        self.count_var = tk.StringVar(value="100")
        ttk.Entry(control_frame, textvariable=self.count_var).pack(fill=tk.X, pady=5)
        
        # 查詢按鈕
        ttk.Button(control_frame, text="獲取數據", 
                  command=self.get_data_thread).pack(fill=tk.X, pady=20)
        
        # 創建右側數據顯示區
        data_frame = ttk.LabelFrame(root, text="數據顯示", padding="10")
        data_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 創建表格
        self.tree = ttk.Treeview(data_frame, show="headings")
        self.tree.pack(fill=tk.BOTH, expand=True)
        
        # 創建圖表區域
        self.fig, self.ax = plt.subplots(figsize=(8, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, master=data_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 狀態欄
        self.status_var = tk.StringVar()
        ttk.Label(root, textvariable=self.status_var).pack(side=tk.BOTTOM, fill=tk.X)
        
    def get_data_thread(self):
        # 在新線程中執行數據獲取
        thread = threading.Thread(target=self.get_data)
        thread.daemon = True
        thread.start()
        
    def get_data(self):
        try:
            self.status_var.set("正在獲取數據...")
            self.root.update()
            
            # 獲取輸入值
            stock_code = self.code_var.get()
            period_map = {
                "1分鐘K線": KLType.K_1M,
                "5分鐘K線": KLType.K_5M,
                "15分鐘K線": KLType.K_15M,
                "30分鐘K線": KLType.K_30M,
                "60分鐘K線": KLType.K_60M,
                "日K線": KLType.K_DAY,
                "週K線": KLType.K_WEEK,
                "月K線": KLType.K_MON
            }
            period = period_map[self.period_var.get()]
            count = min(int(self.count_var.get()), 1000)
            
            # 連接富途API
            quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
            
            try:
                # 獲取K線數據
                ret, data = quote_ctx.get_history_kline(
                    code=stock_code,
                    start=None,
                    end=None,
                    ktype=period,
                    max_count=count
                )
                
                if ret == RET_OK:
                    # 更新表格
                    self.update_table(data)
                    # 更新圖表
                    self.update_chart(data)
                    # 保存數據
                    filename = f"{stock_code.replace('.', '_')}_{datetime.now().strftime('%Y%m%d')}.csv"
                    data.to_csv(filename)
                    self.status_var.set(f"數據已保存到: {filename}")
                else:
                    messagebox.showerror("錯誤", f"獲取數據失敗: {data}")
                    
            finally:
                quote_ctx.close()
                
        except Exception as e:
            messagebox.showerror("錯誤", str(e))
            self.status_var.set("發生錯誤")
            
    def update_table(self, data):
        # 清空現有數據
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 設置列
        columns = ['time_key', 'open', 'close', 'high', 'low', 'volume', 'turnover']
        self.tree['columns'] = columns
        
        # 設置列標題
        column_names = {
            'time_key': '時間',
            'open': '開盤價',
            'close': '收盤價',
            'high': '最高價',
            'low': '最低價',
            'volume': '成交量',
            'turnover': '成交額'
        }
        
        for col in columns:
            self.tree.heading(col, text=column_names[col])
            self.tree.column(col, width=100)
            
        # 插入數據
        for idx, row in data.iterrows():
            values = [row[col] for col in columns]
            self.tree.insert('', 0, values=values)
            
    def update_chart(self, data):
        # 清空圖表
        self.ax.clear()
        
        # 轉換數據格式為mplfinance可用的格式
        data.set_index('time_key', inplace=True)
        
        # 繪製K線圖
        mpf.plot(data, type='candle', style='charles',
                title=f"{self.code_var.get()} K線圖",
                ylabel='價格',
                ylabel_lower='成交量',
                volume=True,
                ax=self.ax,
                volume_panel=2,
                show_nontrading=False)
        
        # 更新畫布
        self.canvas.draw()

if __name__ == "__main__":
    root = tk.Tk()
    app = KLineApp(root)
    root.mainloop() 