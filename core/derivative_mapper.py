"""
衍生品價格映射器
用於將原商品價格映射到認購證/認沽證價格
"""
import json
import os
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd

class DerivativeMapper:
    """衍生品價格映射器"""
    
    def __init__(self, config_path: str = "config/derivative_mapping.json"):
        """
        初始化衍生品映射器
        
        Args:
            config_path: 映射配置文件路徑
        """
        self.config_path = config_path
        self.mappings = {}
        self.price_history = {}  # 存儲價格歷史
        self.load_mappings()
    
    def load_mappings(self):
        """加載映射配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.mappings = data.get('mappings', {})
                    self.price_history = data.get('price_history', {})
            else:
                self.create_default_mappings()
        except Exception as e:
            print(f"加載衍生品映射配置失敗: {e}")
            self.create_default_mappings()
    
    def create_default_mappings(self):
        """創建默認映射配置"""
        self.mappings = {
            "nikkei_call": {
                "name": "日經認購證",
                "underlying_symbol": "hk50.cash",  # 原商品代碼
                "derivative_code": "HK.28123",     # 認購證代碼
                "option_type": "call",             # call/put
                "strike_price": 40000,             # 行權價
                "expiry_date": "2025-03-28",       # 到期日
                "delta": 0.5,                      # Delta值
                "gamma": 0.001,                    # Gamma值
                "theta": -0.01,                    # Theta值
                "volatility": 0.2,                 # 波動率
                "risk_free_rate": 0.03,            # 無風險利率
                "price_multiplier": 1000,          # 價格倍數
                "min_price": 0.001,                # 最小價格
                "max_price": 10.0,                 # 最大價格
                "enabled": True
            },
            "nikkei_put": {
                "name": "日經認沽證",
                "underlying_symbol": "hk50.cash",
                "derivative_code": "HK.28124",
                "option_type": "put",
                "strike_price": 40000,
                "expiry_date": "2025-03-28",
                "delta": -0.5,
                "gamma": 0.001,
                "theta": -0.01,
                "volatility": 0.2,
                "risk_free_rate": 0.03,
                "price_multiplier": 1000,
                "min_price": 0.001,
                "max_price": 10.0,
                "enabled": True
            }
        }
        self.save_mappings()
    
    def save_mappings(self):
        """保存映射配置"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            data = {
                'mappings': self.mappings,
                'price_history': self.price_history,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存衍生品映射配置失敗: {e}")
    
    def add_mapping(self, mapping_id: str, config: Dict):
        """添加新的映射配置"""
        self.mappings[mapping_id] = config
        self.save_mappings()
    
    def update_price_history(self, symbol: str, price: float, timestamp: str = None):
        """更新價格歷史"""
        if timestamp is None:
            timestamp = datetime.now().isoformat()
        
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append({
            'price': price,
            'timestamp': timestamp
        })
        
        # 只保留最近24小時的數據
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.price_history[symbol] = [
            entry for entry in self.price_history[symbol]
            if datetime.fromisoformat(entry['timestamp']) > cutoff_time
        ]
    
    def get_price_range(self, symbol: str, hours: int = 1) -> Tuple[float, float]:
        """
        獲取指定時間內的價格範圍
        
        Args:
            symbol: 商品代碼
            hours: 時間範圍（小時）
            
        Returns:
            (最低價, 最高價)
        """
        if symbol not in self.price_history:
            return None, None
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_prices = [
            entry['price'] for entry in self.price_history[symbol]
            if datetime.fromisoformat(entry['timestamp']) > cutoff_time
        ]
        
        if not recent_prices:
            return None, None
        
        return min(recent_prices), max(recent_prices)
    
    def calculate_option_price(self, underlying_price: float, mapping_config: Dict) -> float:
        """
        使用Black-Scholes模型計算期權價格
        
        Args:
            underlying_price: 標的資產價格
            mapping_config: 映射配置
            
        Returns:
            期權價格
        """
        try:
            S = underlying_price  # 標的價格
            K = mapping_config['strike_price']  # 行權價
            r = mapping_config['risk_free_rate']  # 無風險利率
            sigma = mapping_config['volatility']  # 波動率
            
            # 計算到期時間（年）
            expiry_date = datetime.strptime(mapping_config['expiry_date'], '%Y-%m-%d')
            T = max((expiry_date - datetime.now()).days / 365.0, 0.001)
            
            # Black-Scholes公式
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            # 標準正態分佈累積函數近似
            def norm_cdf(x):
                return 0.5 * (1 + math.erf(x / math.sqrt(2)))
            
            if mapping_config['option_type'] == 'call':
                # 認購證價格
                price = S * norm_cdf(d1) - K * math.exp(-r * T) * norm_cdf(d2)
            else:
                # 認沽證價格
                price = K * math.exp(-r * T) * norm_cdf(-d2) - S * norm_cdf(-d1)
            
            # 應用價格倍數
            price = price / mapping_config.get('price_multiplier', 1000)
            
            # 限制價格範圍
            min_price = mapping_config.get('min_price', 0.001)
            max_price = mapping_config.get('max_price', 10.0)
            price = max(min_price, min(max_price, price))
            
            return round(price, 3)
            
        except Exception as e:
            print(f"計算期權價格失敗: {e}")
            return 0.001
    
    def calculate_simple_mapping(self, underlying_price: float, mapping_config: Dict) -> float:
        """
        簡單線性映射計算（備用方法）
        
        Args:
            underlying_price: 標的資產價格
            mapping_config: 映射配置
            
        Returns:
            衍生品價格
        """
        try:
            strike_price = mapping_config['strike_price']
            delta = mapping_config.get('delta', 0.5)
            
            # 計算價格差異
            price_diff = underlying_price - strike_price
            
            if mapping_config['option_type'] == 'call':
                # 認購證：標的價格高於行權價時有內在價值
                intrinsic_value = max(0, price_diff)
            else:
                # 認沽證：標的價格低於行權價時有內在價值
                intrinsic_value = max(0, -price_diff)
            
            # 應用Delta和價格倍數
            price = (intrinsic_value * abs(delta)) / mapping_config.get('price_multiplier', 1000)
            
            # 添加時間價值（簡化）
            time_value = mapping_config.get('time_value', 0.01)
            price += time_value
            
            # 限制價格範圍
            min_price = mapping_config.get('min_price', 0.001)
            max_price = mapping_config.get('max_price', 10.0)
            price = max(min_price, min(max_price, price))
            
            return round(price, 3)
            
        except Exception as e:
            print(f"簡單映射計算失敗: {e}")
            return 0.001
    
    def map_price(self, underlying_symbol: str, underlying_price: float, 
                  mapping_id: str = None, use_black_scholes: bool = True) -> Optional[Dict]:
        """
        將原商品價格映射到衍生品價格
        
        Args:
            underlying_symbol: 原商品代碼
            underlying_price: 原商品價格
            mapping_id: 指定的映射ID，None表示自動匹配
            use_black_scholes: 是否使用Black-Scholes模型
            
        Returns:
            映射結果字典
        """
        try:
            # 更新價格歷史
            self.update_price_history(underlying_symbol, underlying_price)
            
            # 查找匹配的映射
            target_mapping = None
            target_id = None
            
            if mapping_id and mapping_id in self.mappings:
                target_mapping = self.mappings[mapping_id]
                target_id = mapping_id
            else:
                # 自動匹配第一個啟用的映射
                for mid, mapping in self.mappings.items():
                    if (mapping.get('enabled', True) and 
                        mapping.get('underlying_symbol', '').lower() == underlying_symbol.lower()):
                        target_mapping = mapping
                        target_id = mid
                        break
            
            if not target_mapping:
                return None
            
            # 計算衍生品價格
            if use_black_scholes:
                derivative_price = self.calculate_option_price(underlying_price, target_mapping)
            else:
                derivative_price = self.calculate_simple_mapping(underlying_price, target_mapping)
            
            # 獲取價格範圍信息
            low_1h, high_1h = self.get_price_range(underlying_symbol, 1)
            
            result = {
                'mapping_id': target_id,
                'mapping_name': target_mapping.get('name', ''),
                'underlying_symbol': underlying_symbol,
                'underlying_price': underlying_price,
                'derivative_code': target_mapping.get('derivative_code', ''),
                'derivative_price': derivative_price,
                'option_type': target_mapping.get('option_type', ''),
                'strike_price': target_mapping.get('strike_price', 0),
                'price_range_1h': {
                    'low': low_1h,
                    'high': high_1h
                },
                'calculation_method': 'black_scholes' if use_black_scholes else 'simple',
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            print(f"價格映射失敗: {e}")
            return None
    
    def get_all_mappings(self) -> Dict:
        """獲取所有映射配置"""
        return self.mappings.copy()
    
    def get_mapping_by_derivative_code(self, derivative_code: str) -> Optional[Tuple[str, Dict]]:
        """根據衍生品代碼查找映射"""
        for mapping_id, mapping in self.mappings.items():
            if mapping.get('derivative_code', '') == derivative_code:
                return mapping_id, mapping
        return None
