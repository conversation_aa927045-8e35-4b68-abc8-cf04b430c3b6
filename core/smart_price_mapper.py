"""
智能價格映射器
基於實時市價快照和K線數據進行精確的SL/TP價格映射
"""
import json
import os
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Callable
import pandas as pd

class SmartPriceMapper:
    """智能價格映射器"""
    
    def __init__(self, futu_client=None, config_path: str = "config/smart_mapping.json"):
        """
        初始化智能價格映射器
        
        Args:
            futu_client: 富途客戶端
            config_path: 配置文件路徑
        """
        self.futu_client = futu_client
        self.config_path = config_path
        self.mappings = {}
        self.market_snapshots = {}  # 存儲市價快照
        self.kline_data = {}        # 存儲K線數據
        self.price_relationships = {}  # 存儲價格關係
        
        # 回調函數
        self.sl_tp_callback: Optional[Callable] = None
        
        # 監控線程
        self.monitoring = False
        self.monitor_thread = None
        
        self.load_config()
    
    def load_config(self):
        """加載配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.mappings = data.get('mappings', {})
            else:
                self.create_default_config()
        except Exception as e:
            print(f"加載智能映射配置失敗: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """創建默認配置"""
        self.mappings = {
            "nikkei_warrant_mapping": {
                "name": "日經認購證映射",
                "underlying_symbol": "hk50.cash",
                "warrant_code": "HK.28123",
                "warrant_name": "日經認購證",
                "enabled": True,
                "monitor_interval": 5,  # 監控間隔(秒)
                "kline_period": "1M",   # K線週期
                "kline_count": 90,      # K線數量
                "price_correlation_factor": 1.0,  # 價格相關係數
                "sl_tp_mapping": {
                    "method": "proportional",  # proportional/fixed/custom
                    "sl_ratio": 0.02,         # 止損比例
                    "tp_ratio": 0.03          # 止盈比例
                }
            }
        }
        self.save_config()
    
    def save_config(self):
        """保存配置"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            data = {
                'mappings': self.mappings,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存智能映射配置失敗: {e}")
    
    def set_sl_tp_callback(self, callback: Callable):
        """設置SL/TP回調函數"""
        self.sl_tp_callback = callback
    
    def add_mapping(self, mapping_id: str, underlying_symbol: str, warrant_code: str, 
                   warrant_name: str = "", **kwargs):
        """
        添加新的映射關係
        
        Args:
            mapping_id: 映射ID
            underlying_symbol: 原商品代碼
            warrant_code: 認購證代碼
            warrant_name: 認購證名稱
            **kwargs: 其他配置參數
        """
        self.mappings[mapping_id] = {
            "name": warrant_name or f"{underlying_symbol} -> {warrant_code}",
            "underlying_symbol": underlying_symbol,
            "warrant_code": warrant_code,
            "warrant_name": warrant_name,
            "enabled": True,
            "monitor_interval": kwargs.get('monitor_interval', 5),
            "kline_period": kwargs.get('kline_period', '1M'),
            "kline_count": kwargs.get('kline_count', 90),
            "price_correlation_factor": kwargs.get('price_correlation_factor', 1.0),
            "sl_tp_mapping": kwargs.get('sl_tp_mapping', {
                "method": "proportional",
                "sl_ratio": 0.02,
                "tp_ratio": 0.03
            })
        }
        self.save_config()
    
    def get_market_snapshot(self, code: str) -> Optional[Dict]:
        """獲取市價快照"""
        if not self.futu_client:
            return None
        
        try:
            from futu import RET_OK
            ret, data = self.futu_client.get_market_snapshot([code])
            
            if ret == RET_OK and len(data) > 0:
                snapshot = {
                    'code': code,
                    'last_price': data.iloc[0]['last_price'],
                    'bid_price': data.iloc[0].get('bid_price', 0),
                    'ask_price': data.iloc[0].get('ask_price', 0),
                    'volume': data.iloc[0].get('volume', 0),
                    'timestamp': datetime.now().isoformat()
                }
                self.market_snapshots[code] = snapshot
                return snapshot
            
        except Exception as e:
            print(f"獲取市價快照失敗 {code}: {e}")
        
        return None
    
    def get_kline_data(self, code: str, period: str = "1M", count: int = 90) -> Optional[pd.DataFrame]:
        """獲取K線數據"""
        if not self.futu_client:
            return None
        
        try:
            from futu import RET_OK, KLType
            
            # 轉換週期
            period_map = {
                "1M": KLType.K_1M,
                "5M": KLType.K_5M,
                "15M": KLType.K_15M,
                "30M": KLType.K_30M,
                "60M": KLType.K_60M,
                "1D": KLType.K_DAY
            }
            
            ktype = period_map.get(period, KLType.K_1M)
            ret, data = self.futu_client.get_history_kline(
                code=code, ktype=ktype, max_count=count
            )
            
            if ret == RET_OK:
                self.kline_data[code] = data
                return data
                
        except Exception as e:
            print(f"獲取K線數據失敗 {code}: {e}")
        
        return None
    
    def calculate_price_relationship(self, underlying_symbol: str, warrant_code: str) -> Optional[Dict]:
        """
        計算價格關係
        
        Args:
            underlying_symbol: 原商品代碼
            warrant_code: 認購證代碼
            
        Returns:
            價格關係字典
        """
        try:
            # 獲取兩個商品的市價快照
            underlying_snapshot = self.get_market_snapshot(underlying_symbol)
            warrant_snapshot = self.get_market_snapshot(warrant_code)
            
            if not underlying_snapshot or not warrant_snapshot:
                return None
            
            # 獲取K線數據
            underlying_kline = self.get_kline_data(underlying_symbol)
            warrant_kline = self.get_kline_data(warrant_code)
            
            if underlying_kline is None or warrant_kline is None:
                return None
            
            # 計算90K的最高價和最低價
            underlying_high = underlying_kline['high'].max()
            underlying_low = underlying_kline['low'].min()
            warrant_high = warrant_kline['high'].max()
            warrant_low = warrant_kline['low'].min()
            
            # 計算價格關係
            relationship = {
                'underlying_current': underlying_snapshot['last_price'],
                'warrant_current': warrant_snapshot['last_price'],
                'underlying_high_90k': underlying_high,
                'underlying_low_90k': underlying_low,
                'warrant_high_90k': warrant_high,
                'warrant_low_90k': warrant_low,
                'price_ratio': warrant_snapshot['last_price'] / underlying_snapshot['last_price'],
                'high_ratio': warrant_high / underlying_high,
                'low_ratio': warrant_low / underlying_low,
                'timestamp': datetime.now().isoformat()
            }
            
            # 存儲關係
            key = f"{underlying_symbol}_{warrant_code}"
            self.price_relationships[key] = relationship
            
            return relationship
            
        except Exception as e:
            print(f"計算價格關係失敗: {e}")
            return None
    
    def map_sl_tp_prices(self, underlying_symbol: str, underlying_sl: float,
                        underlying_tp: float, mapping_id: str = None,
                        external_data: Dict = None) -> Optional[Dict]:
        """
        映射SL/TP價格到認購證

        Args:
            underlying_symbol: 原商品代碼
            underlying_sl: 原商品止損價
            underlying_tp: 原商品止盈價
            mapping_id: 映射ID
            external_data: 外部數據（可包含90K最高最低價）

        Returns:
            映射結果
        """
        try:
            # 查找映射配置
            mapping_config = None
            if mapping_id and mapping_id in self.mappings:
                mapping_config = self.mappings[mapping_id]
            else:
                # 自動查找匹配的映射
                for config in self.mappings.values():
                    if config.get('underlying_symbol') == underlying_symbol and config.get('enabled', True):
                        mapping_config = config
                        break
            
            if not mapping_config:
                return None
            
            warrant_code = mapping_config['warrant_code']

            # 檢查是否有外部提供的90K數據
            if external_data and self._has_external_kline_data(external_data):
                # 使用外部提供的90K數據
                relationship = self._create_relationship_from_external_data(
                    underlying_symbol, warrant_code, external_data
                )
            else:
                # 使用系統自動獲取的數據
                relationship = self.calculate_price_relationship(underlying_symbol, warrant_code)

            if not relationship:
                return None
            
            # 獲取當前價格
            underlying_current = relationship['underlying_current']
            warrant_current = relationship['warrant_current']
            
            # 計算價格變化比例
            sl_change_ratio = (underlying_sl - underlying_current) / underlying_current
            tp_change_ratio = (underlying_tp - underlying_current) / underlying_current
            
            # 方法1: 比例映射 (基於90K最高最低價範圍)
            if mapping_config['sl_tp_mapping']['method'] == 'proportional':
                # 使用90K的價格範圍進行映射
                underlying_range = relationship['underlying_high_90k'] - relationship['underlying_low_90k']
                warrant_range = relationship['warrant_high_90k'] - relationship['warrant_low_90k']
                
                if underlying_range > 0:
                    range_ratio = warrant_range / underlying_range
                    
                    # 計算認購證的SL/TP
                    warrant_sl = warrant_current + (underlying_sl - underlying_current) * range_ratio
                    warrant_tp = warrant_current + (underlying_tp - underlying_current) * range_ratio
                else:
                    # 備用方法：使用當前價格比例
                    price_ratio = warrant_current / underlying_current
                    warrant_sl = warrant_current + (underlying_sl - underlying_current) * price_ratio
                    warrant_tp = warrant_current + (underlying_tp - underlying_current) * price_ratio
            
            # 方法2: 固定比例
            elif mapping_config['sl_tp_mapping']['method'] == 'fixed':
                sl_ratio = mapping_config['sl_tp_mapping'].get('sl_ratio', 0.02)
                tp_ratio = mapping_config['sl_tp_mapping'].get('tp_ratio', 0.03)
                
                warrant_sl = warrant_current * (1 - sl_ratio)
                warrant_tp = warrant_current * (1 + tp_ratio)
            
            # 方法3: 自定義映射
            else:
                # 使用簡單的價格比例
                price_ratio = relationship['price_ratio']
                warrant_sl = warrant_current + (underlying_sl - underlying_current) * price_ratio
                warrant_tp = warrant_current + (underlying_tp - underlying_current) * price_ratio
            
            # 確保價格合理性
            warrant_sl = max(0.001, warrant_sl)
            warrant_tp = max(0.001, warrant_tp)
            
            result = {
                'mapping_id': mapping_id,
                'underlying_symbol': underlying_symbol,
                'warrant_code': warrant_code,
                'underlying_current': underlying_current,
                'warrant_current': warrant_current,
                'underlying_sl': underlying_sl,
                'underlying_tp': underlying_tp,
                'warrant_sl': round(warrant_sl, 3),
                'warrant_tp': round(warrant_tp, 3),
                'price_relationship': relationship,
                'mapping_method': mapping_config['sl_tp_mapping']['method'],
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            print(f"映射SL/TP價格失敗: {e}")
            return None
    
    def handle_api_signal(self, signal_data: Dict) -> Optional[Dict]:
        """
        處理API信號
        
        Args:
            signal_data: API信號數據，包含symbol, sl, tp等
            
        Returns:
            映射結果
        """
        try:
            underlying_symbol = signal_data.get('symbol')
            underlying_sl = float(signal_data.get('sl', 0))
            underlying_tp = float(signal_data.get('tp', 0))
            
            if not underlying_symbol or underlying_sl <= 0 or underlying_tp <= 0:
                return None
            
            # 執行SL/TP映射
            result = self.map_sl_tp_prices(underlying_symbol, underlying_sl, underlying_tp)
            
            if result and self.sl_tp_callback:
                # 調用回調函數更新GUI
                self.sl_tp_callback(result)
            
            return result
            
        except Exception as e:
            print(f"處理API信號失敗: {e}")
            return None
    
    def start_monitoring(self, mapping_id: str):
        """開始監控價格關係"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, args=(mapping_id,), daemon=True
        )
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_loop(self, mapping_id: str):
        """監控循環"""
        while self.monitoring:
            try:
                if mapping_id in self.mappings:
                    config = self.mappings[mapping_id]
                    underlying_symbol = config['underlying_symbol']
                    warrant_code = config['warrant_code']
                    
                    # 更新價格關係
                    self.calculate_price_relationship(underlying_symbol, warrant_code)
                    
                    # 等待下次更新
                    interval = config.get('monitor_interval', 5)
                    time.sleep(interval)
                else:
                    break
                    
            except Exception as e:
                print(f"監控循環錯誤: {e}")
                time.sleep(5)
    
    def get_all_mappings(self) -> Dict:
        """獲取所有映射配置"""
        return self.mappings.copy()
    
    def get_price_relationship(self, underlying_symbol: str, warrant_code: str) -> Optional[Dict]:
        """獲取價格關係"""
        key = f"{underlying_symbol}_{warrant_code}"
        return self.price_relationships.get(key)

    def _has_external_kline_data(self, external_data: Dict) -> bool:
        """檢查外部數據是否包含90K線數據"""
        required_fields = [
            'underlying_high_90k', 'underlying_low_90k',
            'warrant_high_90k', 'warrant_low_90k'
        ]
        return all(field in external_data and external_data[field] is not None
                  for field in required_fields)

    def _create_relationship_from_external_data(self, underlying_symbol: str,
                                              warrant_code: str, external_data: Dict) -> Optional[Dict]:
        """從外部數據創建價格關係"""
        try:
            # 獲取當前價格（如果外部數據有提供，否則嘗試從API獲取）
            underlying_current = external_data.get('price', 0)
            warrant_current = external_data.get('warrant_price', 0)

            # 如果沒有當前價格，嘗試從市價快照獲取
            if underlying_current <= 0:
                underlying_snapshot = self.get_market_snapshot(underlying_symbol)
                underlying_current = underlying_snapshot['last_price'] if underlying_snapshot else 0

            if warrant_current <= 0:
                warrant_snapshot = self.get_market_snapshot(warrant_code)
                warrant_current = warrant_snapshot['last_price'] if warrant_snapshot else 0

            # 使用外部提供的90K數據
            underlying_high = float(external_data['underlying_high_90k'])
            underlying_low = float(external_data['underlying_low_90k'])
            warrant_high = float(external_data['warrant_high_90k'])
            warrant_low = float(external_data['warrant_low_90k'])

            # 創建價格關係
            relationship = {
                'underlying_current': underlying_current,
                'warrant_current': warrant_current,
                'underlying_high_90k': underlying_high,
                'underlying_low_90k': underlying_low,
                'warrant_high_90k': warrant_high,
                'warrant_low_90k': warrant_low,
                'price_ratio': warrant_current / underlying_current if underlying_current > 0 else 0,
                'high_ratio': warrant_high / underlying_high if underlying_high > 0 else 0,
                'low_ratio': warrant_low / underlying_low if underlying_low > 0 else 0,
                'data_source': 'external',
                'timestamp': datetime.now().isoformat()
            }

            # 存儲關係
            key = f"{underlying_symbol}_{warrant_code}"
            self.price_relationships[key] = relationship

            return relationship

        except Exception as e:
            print(f"從外部數據創建價格關係失敗: {e}")
            return None
