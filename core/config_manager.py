"""
配置管理器
負責加載和管理系統配置
"""
import json
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器類"""
    
    def __init__(self, config_path: str = "config/settings.json"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路徑
        """
        self.config_path = config_path
        self.config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加載配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # 如果配置文件不存在，創建默認配置
                self.create_default_config()
        except Exception as e:
            print(f"加載配置文件失敗: {e}")
            self.create_default_config()
    
    def create_default_config(self) -> None:
        """創建默認配置"""
        self.config = {
            "futu_api": {
                "host": "127.0.0.1",
                "port": 11111,
                "trade_password": "",
                "security_firm": "FUTUSECURITIES"
            },
            "gui": {
                "window_title": "富途量化交易系統",
                "window_size": "1400x900",
                "theme": "default"
            },
            "trading": {
                "default_environment": "simulate",
                "confirm_real_trades": True
            }
        }
        self.save_config()
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失敗: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        獲取配置值
        
        Args:
            key: 配置鍵，支持點號分隔的嵌套鍵
            default: 默認值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        設置配置值
        
        Args:
            key: 配置鍵，支持點號分隔的嵌套鍵
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 創建嵌套字典結構
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()
    
    def get_futu_config(self) -> Dict[str, Any]:
        """獲取富途API配置"""
        return self.get('futu_api', {})
    
    def get_gui_config(self) -> Dict[str, Any]:
        """獲取GUI配置"""
        return self.get('gui', {})
    
    def get_trading_config(self) -> Dict[str, Any]:
        """獲取交易配置"""
        return self.get('trading', {})
