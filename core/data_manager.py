"""
數據管理器
負責數據的存儲、讀取和管理
"""
import os
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Any, List
import json

class DataManager:
    """數據管理器類"""
    
    def __init__(self, data_path: str = "./data/"):
        """
        初始化數據管理器
        
        Args:
            data_path: 數據存儲路徑
        """
        self.data_path = data_path
        self.kline_path = os.path.join(data_path, "kline")
        self.trade_path = os.path.join(data_path, "trades")
        self.log_path = os.path.join(data_path, "logs")
        
        # 創建目錄
        self._create_directories()
    
    def _create_directories(self):
        """創建必要的目錄"""
        for path in [self.data_path, self.kline_path, self.trade_path, self.log_path]:
            os.makedirs(path, exist_ok=True)
    
    def save_kline_data(self, code: str, data: pd.DataFrame, period: str = "DAY") -> str:
        """
        保存K線數據
        
        Args:
            code: 股票代碼
            data: K線數據
            period: 時間週期
            
        Returns:
            保存的文件路徑
        """
        try:
            # 清理股票代碼中的特殊字符
            clean_code = code.replace('.', '_').replace(':', '_')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{clean_code}_{period}_{timestamp}.csv"
            filepath = os.path.join(self.kline_path, filename)
            
            # 保存數據
            data.to_csv(filepath, index=True, encoding='utf-8-sig')
            
            # 同時保存最新數據（覆蓋模式）
            latest_filename = f"{clean_code}_{period}_latest.csv"
            latest_filepath = os.path.join(self.kline_path, latest_filename)
            data.to_csv(latest_filepath, index=True, encoding='utf-8-sig')
            
            return filepath
            
        except Exception as e:
            print(f"保存K線數據失敗: {e}")
            return ""
    
    def load_kline_data(self, code: str, period: str = "DAY", latest: bool = True) -> Optional[pd.DataFrame]:
        """
        加載K線數據
        
        Args:
            code: 股票代碼
            period: 時間週期
            latest: 是否加載最新數據
            
        Returns:
            K線數據DataFrame
        """
        try:
            clean_code = code.replace('.', '_').replace(':', '_')
            
            if latest:
                filename = f"{clean_code}_{period}_latest.csv"
            else:
                # 查找最新的歷史文件
                pattern = f"{clean_code}_{period}_"
                files = [f for f in os.listdir(self.kline_path) if f.startswith(pattern) and f.endswith('.csv')]
                if not files:
                    return None
                filename = sorted(files)[-1]  # 取最新的文件
            
            filepath = os.path.join(self.kline_path, filename)
            
            if os.path.exists(filepath):
                return pd.read_csv(filepath, index_col=0)
            else:
                return None
                
        except Exception as e:
            print(f"加載K線數據失敗: {e}")
            return None
    
    def save_trade_record(self, trade_data: Dict[str, Any]) -> str:
        """
        保存交易記錄
        
        Args:
            trade_data: 交易數據
            
        Returns:
            保存的文件路徑
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d')
            filename = f"trades_{timestamp}.json"
            filepath = os.path.join(self.trade_path, filename)
            
            # 添加時間戳
            trade_data['timestamp'] = datetime.now().isoformat()
            
            # 如果文件存在，追加數據；否則創建新文件
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    trades = json.load(f)
            else:
                trades = []
            
            trades.append(trade_data)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(trades, f, indent=2, ensure_ascii=False)
            
            return filepath
            
        except Exception as e:
            print(f"保存交易記錄失敗: {e}")
            return ""
    
    def load_trade_records(self, date: str = None) -> List[Dict[str, Any]]:
        """
        加載交易記錄
        
        Args:
            date: 日期（YYYYMMDD格式），None表示今天
            
        Returns:
            交易記錄列表
        """
        try:
            if date is None:
                date = datetime.now().strftime('%Y%m%d')
            
            filename = f"trades_{date}.json"
            filepath = os.path.join(self.trade_path, filename)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return []
                
        except Exception as e:
            print(f"加載交易記錄失敗: {e}")
            return []
    
    def save_log(self, level: str, message: str, category: str = "general"):
        """
        保存日誌
        
        Args:
            level: 日誌級別（INFO, WARNING, ERROR）
            message: 日誌消息
            category: 日誌分類
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d')
            filename = f"{category}_{timestamp}.log"
            filepath = os.path.join(self.log_path, filename)
            
            log_entry = f"[{datetime.now().isoformat()}] [{level}] {message}\n"
            
            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
        except Exception as e:
            print(f"保存日誌失敗: {e}")
    
    def get_available_kline_files(self) -> List[str]:
        """
        獲取可用的K線文件列表
        
        Returns:
            文件名列表
        """
        try:
            files = [f for f in os.listdir(self.kline_path) if f.endswith('.csv')]
            return sorted(files, reverse=True)  # 按時間倒序
        except Exception as e:
            print(f"獲取K線文件列表失敗: {e}")
            return []
    
    def cleanup_old_files(self, days: int = 30):
        """
        清理舊文件
        
        Args:
            days: 保留天數
        """
        try:
            import time
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            
            for root, dirs, files in os.walk(self.data_path):
                for file in files:
                    filepath = os.path.join(root, file)
                    if os.path.getmtime(filepath) < cutoff_time:
                        os.remove(filepath)
                        print(f"已刪除舊文件: {filepath}")
                        
        except Exception as e:
            print(f"清理舊文件失敗: {e}")
    
    def export_data(self, export_path: str, data_type: str = "all"):
        """
        導出數據
        
        Args:
            export_path: 導出路徑
            data_type: 數據類型（kline, trades, logs, all）
        """
        try:
            import shutil
            
            if data_type in ["kline", "all"]:
                kline_export = os.path.join(export_path, "kline")
                if os.path.exists(self.kline_path):
                    shutil.copytree(self.kline_path, kline_export, dirs_exist_ok=True)
            
            if data_type in ["trades", "all"]:
                trade_export = os.path.join(export_path, "trades")
                if os.path.exists(self.trade_path):
                    shutil.copytree(self.trade_path, trade_export, dirs_exist_ok=True)
            
            if data_type in ["logs", "all"]:
                log_export = os.path.join(export_path, "logs")
                if os.path.exists(self.log_path):
                    shutil.copytree(self.log_path, log_export, dirs_exist_ok=True)
            
            print(f"數據導出完成: {export_path}")
            
        except Exception as e:
            print(f"導出數據失敗: {e}")
