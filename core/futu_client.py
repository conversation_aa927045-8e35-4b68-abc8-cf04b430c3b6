"""
富途API客戶端封裝
統一管理富途API連接和操作
"""
from futu import *
import threading
import time
from typing import Optional, Dict, Any, Callable, List, Tuple
from datetime import datetime

class FutuClient:
    """富途API客戶端類"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化富途客戶端
        
        Args:
            config: 富途API配置
        """
        self.config = config
        self.quote_ctx: Optional[OpenQuoteContext] = None
        self.trade_ctx: Optional[OpenSecTradeContext] = None
        self.is_connected = False
        self.is_trade_unlocked = False
        
        # 回調函數
        self.status_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        # 連接鎖
        self._lock = threading.Lock()
    
    def set_callbacks(self, status_callback: Callable = None, error_callback: Callable = None):
        """設置回調函數"""
        self.status_callback = status_callback
        self.error_callback = error_callback
    
    def _update_status(self, message: str):
        """更新狀態"""
        if self.status_callback:
            self.status_callback(message)
    
    def _handle_error(self, title: str, message: str):
        """處理錯誤"""
        if self.error_callback:
            self.error_callback(title, message)
    
    def connect(self) -> bool:
        """
        連接富途API
        
        Returns:
            是否連接成功
        """
        with self._lock:
            try:
                # 連接行情API
                if self.quote_ctx is None:
                    self.quote_ctx = OpenQuoteContext(
                        host=self.config.get('host', '127.0.0.1'),
                        port=self.config.get('port', 11111)
                    )
                
                # 連接交易API
                if self.trade_ctx is None:
                    self.trade_ctx = OpenSecTradeContext(
                        filter_trdmarket=TrdMarket.HK,
                        host=self.config.get('host', '127.0.0.1'),
                        port=self.config.get('port', 11111),
                        security_firm=SecurityFirm.FUTUSECURITIES
                    )
                
                self.is_connected = True
                self._update_status("已連接到富途服務器")
                
                # 嘗試解鎖交易
                self.unlock_trade()
                
                return True
                
            except Exception as e:
                error_msg = f"連接富途API失敗: {str(e)}"
                self._handle_error("連接錯誤", error_msg)
                return False
    
    def unlock_trade(self) -> bool:
        """
        解鎖交易功能
        
        Returns:
            是否解鎖成功
        """
        if not self.trade_ctx:
            return False
        
        try:
            pwd = self.config.get('trade_password', '')
            if not pwd:
                self._update_status("未設置交易密碼，跳過解鎖")
                return False
            
            ret, data = self.trade_ctx.unlock_trade(pwd)
            if ret == RET_OK:
                self.is_trade_unlocked = True
                self._update_status("交易功能已解鎖")
                return True
            else:
                error_msg = f"解鎖交易失敗: {data}"
                self._handle_error("解鎖錯誤", error_msg)
                return False
                
        except Exception as e:
            error_msg = f"解鎖交易異常: {str(e)}"
            self._handle_error("解鎖錯誤", error_msg)
            return False
    
    def disconnect(self):
        """斷開連接"""
        with self._lock:
            try:
                if self.quote_ctx:
                    self.quote_ctx.close()
                    self.quote_ctx = None
                
                if self.trade_ctx:
                    self.trade_ctx.close()
                    self.trade_ctx = None
                
                self.is_connected = False
                self.is_trade_unlocked = False
                self._update_status("已斷開富途連接")
                
            except Exception as e:
                print(f"斷開連接時出錯: {e}")
    
    def get_market_snapshot(self, codes: List[str]) -> Tuple[int, Any]:
        """
        獲取市場快照
        
        Args:
            codes: 股票代碼列表
            
        Returns:
            (ret_code, data)
        """
        if not self.quote_ctx:
            return RET_ERROR, "未連接行情API"
        
        try:
            return self.quote_ctx.get_market_snapshot(codes)
        except Exception as e:
            return RET_ERROR, str(e)
    
    def get_history_kline(self, code: str, start: str = None, end: str = None, 
                         ktype: KLType = KLType.K_DAY, max_count: int = 100) -> Tuple[int, Any]:
        """
        獲取歷史K線數據
        
        Args:
            code: 股票代碼
            start: 開始時間
            end: 結束時間
            ktype: K線類型
            max_count: 最大數量
            
        Returns:
            (ret_code, data)
        """
        if not self.quote_ctx:
            return RET_ERROR, "未連接行情API"
        
        try:
            return self.quote_ctx.get_history_kline(
                code=code, start=start, end=end, ktype=ktype, max_count=max_count
            )
        except Exception as e:
            return RET_ERROR, str(e)
    
    def place_order(self, price: float, qty: int, code: str, trd_side: TrdSide,
                   order_type: OrderType = OrderType.NORMAL, 
                   trd_env: TrdEnv = TrdEnv.SIMULATE) -> Tuple[int, Any]:
        """
        下單
        
        Args:
            price: 價格
            qty: 數量
            code: 股票代碼
            trd_side: 買賣方向
            order_type: 訂單類型
            trd_env: 交易環境
            
        Returns:
            (ret_code, data)
        """
        if not self.trade_ctx:
            return RET_ERROR, "未連接交易API"
        
        if not self.is_trade_unlocked:
            return RET_ERROR, "交易功能未解鎖"
        
        try:
            return self.trade_ctx.place_order(
                price=price, qty=qty, code=code, trd_side=trd_side,
                order_type=order_type, trd_env=trd_env
            )
        except Exception as e:
            return RET_ERROR, str(e)
    
    def get_order_list(self, trd_env: TrdEnv = TrdEnv.SIMULATE) -> Tuple[int, Any]:
        """
        獲取訂單列表
        
        Args:
            trd_env: 交易環境
            
        Returns:
            (ret_code, data)
        """
        if not self.trade_ctx:
            return RET_ERROR, "未連接交易API"
        
        try:
            return self.trade_ctx.order_list_query(trd_env=trd_env)
        except Exception as e:
            return RET_ERROR, str(e)
    
    def cancel_all_orders(self, trd_env: TrdEnv = TrdEnv.SIMULATE) -> Tuple[int, Any]:
        """
        撤銷所有訂單
        
        Args:
            trd_env: 交易環境
            
        Returns:
            (ret_code, data)
        """
        if not self.trade_ctx:
            return RET_ERROR, "未連接交易API"
        
        try:
            # 先獲取訂單列表
            ret, orders = self.get_order_list(trd_env)
            if ret != RET_OK:
                return ret, orders
            
            # 撤銷每個訂單
            results = []
            for _, order in orders.iterrows():
                if order['order_status'] in [OrderStatus.SUBMITTED, OrderStatus.FILLED_PART]:
                    ret, data = self.trade_ctx.modify_order(
                        ModifyOrderOp.CANCEL, order['order_id'], 0, 0, trd_env=trd_env
                    )
                    results.append((order['order_id'], ret, data))
            
            return RET_OK, results
            
        except Exception as e:
            return RET_ERROR, str(e)
