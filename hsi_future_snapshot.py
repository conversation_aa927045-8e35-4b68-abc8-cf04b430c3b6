from futu import *
import time
from datetime import datetime

def monitor_hsi_future():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        # 使用正確的期貨代碼格式
        future_codes = ['HK.HSI_2403', 'HK.MHI_2403']  # 大小恆指期貨，2024年3月合約
        print(f"開始監控期貨合約: {', '.join(future_codes)}")
        
        while True:
            start_time = time.time()
            local_time = datetime.now()
            
            # 獲取快照
            ret, data = quote_ctx.get_market_snapshot(future_codes)
            end_time = time.time()
            
            if ret == RET_OK:
                print(f"\n[{local_time.strftime('%H:%M:%S.%f')[:-3]}]")  # 顯示到毫秒
                print(f"延遲: {((end_time - start_time) * 1000):.2f} 毫秒")
                
                # 顯示每個期貨合約的數據
                for idx in range(len(future_codes)):
                    print(f"\n{data['code'][idx]}")
                    print(f"最新價: {data['last_price'][idx]}")
                    print(f"買價: {data['ask_price'][idx]}")
                    print(f"賣價: {data['bid_price'][idx]}")
                    print(f"成交量: {data['volume'][idx]:,}")
                    print(f"成交額: {data['turnover'][idx]:,.0f}")
                    print(f"今開: {data['open_price'][idx]}")
                    print(f"今高: {data['high_price'][idx]}")
                    print(f"今低: {data['low_price'][idx]}")
                    print(f"升跌: {data['last_price'][idx] - data['open_price'][idx]:.1f}")
                    print(f"升跌%: {((data['last_price'][idx] - data['open_price'][idx]) / data['open_price'][idx] * 100):.2f}%")
                    
                print("-" * 50)
            else:
                print(f"獲取數據失敗: {data}")
                # 如果是權限問題，顯示詳細信息
                if isinstance(data, dict) and 'error_msg' in data:
                    print(f"錯誤信息: {data['error_msg']}")
                    if '無權限' in str(data):
                        print("\n提示：獲取期貨數據需要期貨行情權限，請在富途牛牛APP中開通")
                        break
            
            # 等待到下一秒
            time.sleep(max(0, 1 - (time.time() - start_time)))
            
    except KeyboardInterrupt:
        print("\n程式已停止")
    except Exception as e:
        print(f"發生異常: {str(e)}")
        import traceback
        print(traceback.format_exc())
    finally:
        quote_ctx.close()

if __name__ == "__main__":
    print("正在連接FutuOpenD...")
    monitor_hsi_future() 