#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
衍生品價格映射測試腳本
測試價格映射功能是否正常工作
"""

import sys
import os
import requests
import json

# 添加項目根目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_derivative_mapper():
    """測試衍生品映射器"""
    try:
        from core.derivative_mapper import DerivativeMapper
        
        print("=== 衍生品價格映射測試 ===\n")
        
        # 創建映射器
        mapper = DerivativeMapper()
        print("✓ 衍生品映射器創建成功")
        
        # 測試不同的原商品價格
        test_prices = [39000, 39500, 40000, 40500, 41000]
        underlying_symbol = "hk50.cash"
        
        print(f"\n測試原商品: {underlying_symbol}")
        print("價格映射結果:")
        print("-" * 80)
        print(f"{'原價格':<8} {'映射ID':<20} {'衍生品代碼':<12} {'計算價格':<10} {'方法':<12}")
        print("-" * 80)
        
        for price in test_prices:
            # 測試所有映射
            mappings = mapper.get_all_mappings()
            
            for mapping_id in mappings.keys():
                # Black-Scholes方法
                result_bs = mapper.map_price(underlying_symbol, price, mapping_id, True)
                if result_bs:
                    print(f"{price:<8} {mapping_id:<20} {result_bs['derivative_code']:<12} {result_bs['derivative_price']:<10.3f} {'BS模型':<12}")
                
                # 簡單映射方法
                result_simple = mapper.map_price(underlying_symbol, price, mapping_id, False)
                if result_simple:
                    print(f"{price:<8} {mapping_id:<20} {result_simple['derivative_code']:<12} {result_simple['derivative_price']:<10.3f} {'簡單映射':<12}")
        
        print("-" * 80)
        print("✓ 價格映射測試完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 衍生品映射測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_integration():
    """測試API整合"""
    print("\n=== API整合測試 ===\n")
    
    # 測試API端點
    api_url = "http://localhost:8888"
    
    # 測試數據
    test_data = {
        "symbol": "hk50.cash",
        "direction": "BUY",
        "price": 40000
    }
    
    try:
        # 測試GET請求
        print("測試API服務器狀態...")
        response = requests.get(api_url, timeout=5)
        if response.status_code == 200:
            print("✓ API服務器運行正常")
        else:
            print("⚠ API服務器響應異常")
        
        # 測試POST請求
        print("測試下單API...")
        response = requests.post(f"{api_url}/order", 
                               json=test_data, 
                               timeout=5)
        
        if response.status_code == 200:
            print("✓ 下單API測試成功")
            print(f"響應: {response.text}")
        else:
            print(f"⚠ 下單API測試失敗: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠ 無法連接到API服務器 (請先啟動trade_gui.py並啟用API)")
        return False
    except Exception as e:
        print(f"✗ API測試失敗: {e}")
        return False

def simulate_trading_scenario():
    """模擬交易場景"""
    print("\n=== 交易場景模擬 ===\n")
    
    try:
        from core.derivative_mapper import DerivativeMapper
        
        mapper = DerivativeMapper()
        
        # 模擬場景：日經指數從39800上漲到40200
        scenarios = [
            {"price": 39800, "description": "開盤價"},
            {"price": 39900, "description": "小幅上漲"},
            {"price": 40000, "description": "突破40000"},
            {"price": 40100, "description": "繼續上漲"},
            {"price": 40200, "description": "高點"}
        ]
        
        print("場景: 日經指數價格變化對認購證/認沽證的影響")
        print("-" * 100)
        print(f"{'時間':<10} {'日經價格':<10} {'描述':<12} {'認購證40000':<15} {'認沽證40000':<15} {'價格變化':<15}")
        print("-" * 100)
        
        prev_call_price = None
        prev_put_price = None
        
        for i, scenario in enumerate(scenarios):
            price = scenario["price"]
            desc = scenario["description"]
            
            # 計算認購證價格
            call_result = mapper.map_price("hk50.cash", price, "nikkei_call_40000", True)
            put_result = mapper.map_price("hk50.cash", price, "nikkei_put_40000", True)
            
            call_price = call_result['derivative_price'] if call_result else 0
            put_price = put_result['derivative_price'] if put_result else 0
            
            # 計算價格變化
            if prev_call_price is not None:
                call_change = call_price - prev_call_price
                put_change = put_price - prev_put_price
                change_text = f"C:{call_change:+.3f} P:{put_change:+.3f}"
            else:
                change_text = "基準"
            
            print(f"T{i+1:<9} {price:<10} {desc:<12} {call_price:<15.3f} {put_price:<15.3f} {change_text:<15}")
            
            prev_call_price = call_price
            prev_put_price = put_price
        
        print("-" * 100)
        print("✓ 交易場景模擬完成")
        
        # 分析結果
        print("\n分析:")
        print("- 當日經指數上漲時，認購證價格上漲，認沽證價格下跌")
        print("- 價格變化符合期權定價理論")
        print("- 可以根據原商品價格變化自動計算衍生品價格")
        
        return True
        
    except Exception as e:
        print(f"✗ 交易場景模擬失敗: {e}")
        return False

def main():
    """主函數"""
    print("富途衍生品價格映射系統測試")
    print("=" * 50)
    
    # 運行測試
    tests = [
        ("衍生品映射器", test_derivative_mapper),
        ("API整合", test_api_integration),
        ("交易場景模擬", simulate_trading_scenario)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n開始測試: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 總結
    print("\n" + "=" * 50)
    print("測試總結:")
    for test_name, result in results:
        status = "✓ 通過" if result else "✗ 失敗"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("\n🎉 所有測試通過！衍生品價格映射系統可以正常使用。")
        print("\n使用說明:")
        print("1. 啟動 trade_gui.py 或 main.py")
        print("2. 在外部API設置中啟用'衍生品價格映射'")
        print("3. 選擇合適的映射ID (如 nikkei_call_40000)")
        print("4. 發送API信號時，系統會自動將原商品價格轉換為衍生品價格")
        print("\nAPI信號格式:")
        print('{"symbol": "hk50.cash", "direction": "BUY", "price": 40000}')
    else:
        print(f"\n⚠️  有 {total-passed} 項測試失敗，請檢查系統配置。")

if __name__ == "__main__":
    main()
