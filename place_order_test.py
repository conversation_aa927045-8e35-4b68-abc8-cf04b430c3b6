from futu import *
import time

def test_place_order():
    # 創建交易對象 - 注意這裡使用 OpenHKTradeContext 因為是港股
    trade_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    try:
        # 先檢查帳戶狀態
        ret, data = trade_ctx.get_acc_list()
        if ret == RET_OK:
            print('獲取到的帳戶列表：')
            print(data)
            
            # 查看今日訂單
            print('\n===== 查詢今日訂單 =====')
            ret, data = trade_ctx.order_list_query(trd_env=TrdEnv.SIMULATE)
            if ret == RET_OK:
                print('今日訂單列表：')
                if len(data) > 0:
                    print(data)
                else:
                    print('今日還沒有訂單')
            
            # 確認交易環境（真實/模擬）
            ret, data = trade_ctx.position_list_query(trd_env=TrdEnv.SIMULATE)  # 使用模擬環境
            if ret == RET_OK:
                print('\n當前持倉：')
                print(data)
                
                # 嘗試下單（以騰訊為例）
                ret, data = trade_ctx.place_order(
                    price=0.040,                # 價格，這裡設置一個較低的價格作為測試
                    qty=10000,                    # 數量
                    code='HK.56975',           # 股票代碼
                    trd_side=TrdSide.BUY,      # 買入
                    order_type=OrderType.NORMAL,# 普通訂單
                    trd_env=TrdEnv.SIMULATE    # 模擬環境
                )
                
                if ret == RET_OK:
                    print('\n下單成功！訂單資料：')
                    print(data)
                    order_id = data['order_id'][0]
                    
                    # 等待一下，讓訂單有時間處理
                    time.sleep(2)
                    
                    # 查詢剛才的訂單狀態
                    print('\n===== 查詢剛才的訂單狀態 =====')
                    ret, data = trade_ctx.order_list_query(order_id=order_id, trd_env=TrdEnv.SIMULATE)
                    if ret == RET_OK:
                        print('訂單狀態：')
                        print(data)
                    else:
                        print('查詢訂單狀態失敗：', data)
                else:
                    print('\n下單失敗：', data)
            else:
                print('獲取持倉失敗：', data)
        else:
            print('獲取帳戶失敗：', data)
            
    except Exception as e:
        print('發生異常:', str(e))
    
    finally:
        # 關閉連接
        trade_ctx.close()

if __name__ == "__main__":
    print("正在連接FutuOpenD...")
    test_place_order() 