Metadata-Version: 2.2
Name: simplejson
Version: 3.20.1
Summary: Simple, fast, extensible JSON encoder/decoder for Python
Home-page: https://github.com/simplejson/simplejson
Author: <PERSON>
Author-email: <EMAIL>
License: MIT License
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: License :: OSI Approved :: Academic Free License (AFL)
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.5
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=2.5, !=3.0.*, !=3.1.*, !=3.2.*
License-File: LICENSE.txt
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: platform
Dynamic: requires-python
Dynamic: summary

simplejson
----------

simplejson is a simple, fast, complete, correct and extensible
JSON <http://json.org> encoder and decoder for Python 3.3+
with legacy support for Python 2.5+.  It is pure Python code
with no dependencies, but includes an optional C extension
for a serious speed boost.

The latest documentation for simplejson can be read online here:
https://simplejson.readthedocs.io/

simplejson is the externally maintained development version of the
json library included with Python (since 2.6). This version is tested
with the latest Python 3.8 and maintains backwards compatibility
with Python 3.3+ and the legacy Python 2.5 - Python 2.7 releases.

The encoder can be specialized to provide serialization in any kind of
situation, without any special support by the objects to be serialized
(somewhat like pickle). This is best done with the ``default`` kwarg
to dumps.

The decoder can handle incoming JSON strings of any specified encoding
(UTF-8 by default). It can also be specialized to post-process JSON
objects with the ``object_hook`` or ``object_pairs_hook`` kwargs. This
is particularly useful for implementing protocols such as JSON-RPC
that have a richer type system than JSON itself.

For those of you that have legacy systems to maintain, there is a
very old fork of simplejson in the `python2.2`_ branch that supports
Python 2.2. This is based on a very old version of simplejson,
is not maintained, and should only be used as a last resort.

.. _python2.2: https://github.com/simplejson/simplejson/tree/python2.2
