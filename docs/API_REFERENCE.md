# API參考文檔

## 核心模塊 (core/)

### ConfigManager 配置管理器

負責系統配置的加載、保存和管理。

#### 主要方法

```python
class ConfigManager:
    def __init__(self, config_path: str = "config/settings.json")
    def load_config(self) -> None
    def save_config(self) -> None
    def get(self, key: str, default: Any = None) -> Any
    def set(self, key: str, value: Any) -> None
    def get_futu_config(self) -> Dict[str, Any]
    def get_gui_config(self) -> Dict[str, Any]
    def get_trading_config(self) -> Dict[str, Any]
```

#### 使用示例

```python
from core import ConfigManager

# 創建配置管理器
config = ConfigManager()

# 獲取配置值
host = config.get('futu_api.host', '127.0.0.1')
window_size = config.get('gui.window_size', '1400x900')

# 設置配置值
config.set('trading.default_environment', 'simulate')

# 獲取特定配置組
futu_config = config.get_futu_config()
```

### FutuClient 富途API客戶端

封裝富途API的調用，提供統一的接口。

#### 主要方法

```python
class FutuClient:
    def __init__(self, config: Dict[str, Any])
    def set_callbacks(self, status_callback: Callable = None, error_callback: Callable = None)
    def connect(self) -> bool
    def disconnect(self)
    def unlock_trade(self) -> bool
    def get_market_snapshot(self, codes: List[str]) -> Tuple[int, Any]
    def get_history_kline(self, code: str, start: str = None, end: str = None, 
                         ktype: KLType = KLType.K_DAY, max_count: int = 100) -> Tuple[int, Any]
    def place_order(self, price: float, qty: int, code: str, trd_side: TrdSide,
                   order_type: OrderType = OrderType.NORMAL, 
                   trd_env: TrdEnv = TrdEnv.SIMULATE) -> Tuple[int, Any]
    def get_order_list(self, trd_env: TrdEnv = TrdEnv.SIMULATE) -> Tuple[int, Any]
    def cancel_all_orders(self, trd_env: TrdEnv = TrdEnv.SIMULATE) -> Tuple[int, Any]
```

#### 使用示例

```python
from core import FutuClient
from futu import *

# 創建客戶端
config = {'host': '127.0.0.1', 'port': 11111, 'trade_password': '123456'}
client = FutuClient(config)

# 設置回調函數
def status_callback(message):
    print(f"狀態: {message}")

def error_callback(title, message):
    print(f"錯誤 {title}: {message}")

client.set_callbacks(status_callback, error_callback)

# 連接API
if client.connect():
    # 獲取市場快照
    ret, data = client.get_market_snapshot(['HK.00700'])
    if ret == RET_OK:
        print(f"騰訊當前價格: {data.iloc[0]['last_price']}")
    
    # 獲取K線數據
    ret, kline_data = client.get_history_kline('HK.00700', ktype=KLType.K_DAY, max_count=50)
    if ret == RET_OK:
        print(f"獲取到 {len(kline_data)} 條K線數據")
    
    # 模擬下單
    ret, order_data = client.place_order(
        price=100.0, qty=100, code='HK.00700', 
        trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE
    )
    if ret == RET_OK:
        print(f"下單成功，訂單ID: {order_data.iloc[0]['order_id']}")

# 斷開連接
client.disconnect()
```

### DataManager 數據管理器

負責數據的存儲、讀取和管理。

#### 主要方法

```python
class DataManager:
    def __init__(self, data_path: str = "./data/")
    def save_kline_data(self, code: str, data: pd.DataFrame, period: str = "DAY") -> str
    def load_kline_data(self, code: str, period: str = "DAY", latest: bool = True) -> Optional[pd.DataFrame]
    def save_trade_record(self, trade_data: Dict[str, Any]) -> str
    def load_trade_records(self, date: str = None) -> List[Dict[str, Any]]
    def save_log(self, level: str, message: str, category: str = "general")
    def get_available_kline_files(self) -> List[str]
    def cleanup_old_files(self, days: int = 30)
    def export_data(self, export_path: str, data_type: str = "all")
```

#### 使用示例

```python
from core import DataManager
import pandas as pd

# 創建數據管理器
data_mgr = DataManager("./data/")

# 保存K線數據
kline_data = pd.DataFrame({
    'time_key': ['2025-01-30 09:30:00'],
    'open': [100.0],
    'high': [102.0],
    'low': [99.0],
    'close': [101.0],
    'volume': [1000000]
})
filepath = data_mgr.save_kline_data('HK.00700', kline_data, 'DAY')
print(f"K線數據已保存到: {filepath}")

# 加載K線數據
loaded_data = data_mgr.load_kline_data('HK.00700', 'DAY')
if loaded_data is not None:
    print(f"加載了 {len(loaded_data)} 條K線數據")

# 保存交易記錄
trade_record = {
    'code': 'HK.00700',
    'direction': 'BUY',
    'price': 100.0,
    'qty': 100,
    'environment': 'simulate'
}
data_mgr.save_trade_record(trade_record)

# 保存日誌
data_mgr.save_log('INFO', '系統啟動', 'system')
data_mgr.save_log('ERROR', '連接失敗', 'network')
```

## GUI模塊 (gui/)

### MainWindow 主窗口

系統的主界面，整合所有功能模塊。

#### 主要方法

```python
class MainWindow:
    def __init__(self)
    def setup_window(self)
    def create_widgets(self)
    def connect_futu(self)
    def disconnect_futu(self)
    def update_status(self, message: str)
    def handle_error(self, title: str, message: str)
    def run(self)
```

### TradingPanel 交易面板

提供股票交易功能的界面。

#### 主要方法

```python
class TradingPanel:
    def __init__(self, parent, futu_client, config_manager, data_manager)
    def place_order(self, direction: str)
    def refresh_orders(self)
    def cancel_all_orders(self)
    def get_quote(self)
    def on_connection_changed(self, connected: bool)
```

### KLinePanel K線面板

提供K線數據查看和圖表顯示功能。

#### 主要方法

```python
class KLinePanel:
    def __init__(self, parent, futu_client, config_manager, data_manager)
    def fetch_kline_data(self)
    def update_chart(self, data)
    def update_table(self, data)
    def save_data(self)
    def load_data(self)
    def export_chart(self)
    def on_connection_changed(self, connected: bool)
```

### MonitoringPanel 監控面板

提供實時監控和價格提醒功能。

#### 主要方法

```python
class MonitoringPanel:
    def __init__(self, parent, futu_client, config_manager, data_manager)
    def add_monitor(self)
    def remove_monitor(self)
    def start_monitoring(self)
    def stop_monitoring(self)
    def monitor_loop(self)
    def trigger_price_alert(self, code, current_price, alert_price)
    def on_connection_changed(self, connected: bool)
```

## 配置文件格式

### settings.json

```json
{
  "futu_api": {
    "host": "127.0.0.1",
    "port": 11111,
    "trade_password": "交易密碼",
    "security_firm": "FUTUSECURITIES"
  },
  "gui": {
    "window_title": "富途量化交易系統 v2.0",
    "window_size": "1400x900",
    "theme": "default",
    "auto_save_data": true,
    "data_save_path": "./data/"
  },
  "trading": {
    "default_environment": "simulate",
    "confirm_real_trades": true,
    "max_order_amount": 1000000,
    "risk_management": {
      "max_position_size": 0.1,
      "stop_loss_percentage": 0.05
    }
  },
  "data": {
    "auto_save_kline": true,
    "kline_data_path": "./data/kline/",
    "max_kline_count": 1000,
    "default_kline_period": "K_DAY"
  },
  "monitoring": {
    "refresh_interval": 1000,
    "enable_sound_alerts": false,
    "price_alert_threshold": 0.02
  }
}
```

## 數據格式

### K線數據格式

```python
# pandas DataFrame格式
columns = ['time_key', 'open', 'high', 'low', 'close', 'volume', 'turnover']
```

### 交易記錄格式

```python
trade_record = {
    'timestamp': '2025-01-30T10:30:00',
    'code': 'HK.00700',
    'direction': 'BUY',  # 'BUY' 或 'SELL'
    'price': 100.0,
    'qty': 100,
    'environment': 'simulate',  # 'simulate' 或 'real'
    'order_id': '12345678'
}
```

### 監控數據格式

```python
monitor_data = {
    'alert_price': 100.0,
    'last_price': 99.5,
    'last_update': '10:30:00'
}
```

## 錯誤處理

### 錯誤代碼

- `RET_OK`: 成功
- `RET_ERROR`: 一般錯誤
- 其他富途API錯誤代碼請參考富途官方文檔

### 異常處理

```python
try:
    ret, data = client.get_market_snapshot(['HK.00700'])
    if ret == RET_OK:
        # 處理成功情況
        pass
    else:
        # 處理API錯誤
        print(f"API錯誤: {data}")
except Exception as e:
    # 處理異常
    print(f"異常: {str(e)}")
```

## 擴展開發

### 添加新的API接口

1. 在 `FutuClient` 類中添加新方法
2. 實現錯誤處理和回調
3. 在相應的GUI面板中調用

### 添加新的數據類型

1. 在 `DataManager` 類中添加保存/加載方法
2. 定義數據格式和文件命名規則
3. 實現數據驗證和清理

### 自定義GUI組件

1. 繼承相應的tkinter組件
2. 實現必要的回調方法
3. 在主窗口中註冊和使用
