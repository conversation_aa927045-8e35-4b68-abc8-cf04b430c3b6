# 富途量化交易系統 v2.0

基於富途OpenAPI開發的現代化量化交易平台，提供完整的行情數據獲取、交易執行、實時監控等功能。

## ✨ 主要特性

- 🔄 **統一界面**: 整合所有功能到一個現代化GUI界面
- 📊 **實時行情**: 支持多市場實時行情數據獲取和顯示
- 💹 **智能交易**: 模擬/實盤交易，支持多種訂單類型
- 📈 **K線圖表**: 專業的K線圖表顯示，支持多時間週期
- 🔍 **實時監控**: 多股票實時監控，價格提醒功能
- 💾 **數據管理**: 自動數據保存，支持歷史數據查詢
- ⚙️ **配置管理**: 靈活的配置系統，支持個性化設置
- 📝 **日誌記錄**: 完整的操作日誌和錯誤記錄

## 🚀 快速開始

### 環境要求

- Python 3.7+
- Windows/macOS/Linux
- FutuOpenD (富途開放接口網關)

### 安裝步驟

1. **克隆項目**
   ```bash
   git clone <repository-url>
   cd futu-trading-system
   ```

2. **安裝依賴**
   ```bash
   pip install -r requirements.txt
   ```

3. **下載並啟動FutuOpenD**
   - 訪問 [富途OpenAPI官網](https://openapi.futunn.com) 下載FutuOpenD
   - 安裝並啟動FutuOpenD
   - 使用富途賬號登錄

4. **運行程序**
   ```bash
   python main.py
   ```

## 📖 使用說明

### 首次使用

1. **連接設置**
   - 確保FutuOpenD已啟動並登錄
   - 點擊主界面的"連接富途"按鈕
   - 系統會自動連接到本地的FutuOpenD服務

2. **配置交易密碼**
   - 如需使用交易功能，請在設置中配置交易密碼
   - 交易密碼是您在富途APP中設置的6位數字密碼

### 主要功能

#### 1. 交易功能
- **模擬交易**: 無風險的模擬交易環境，適合學習和測試
- **實盤交易**: 真實的股票交易，需要解鎖交易功能
- **訂單管理**: 查看、修改、撤銷訂單
- **快速下單**: 支持快速金額設置和一鍵下單

#### 2. K線圖表
- **多時間週期**: 支持1分鐘到月K線的多種時間週期
- **專業圖表**: 基於matplotlib和mplfinance的專業K線圖
- **數據保存**: 自動保存K線數據到本地
- **圖表導出**: 支持將圖表導出為圖片文件

#### 3. 實時監控
- **多股票監控**: 同時監控多只股票的實時價格
- **價格提醒**: 設置目標價格，到達時自動提醒
- **實時更新**: 可配置的刷新間隔，實時更新價格信息

### 支持的市場和品種

| 市場 | 股票 | ETF | 期權 | 期貨 | 窩輪牛熊證 |
|------|------|-----|------|------|------------|
| 香港 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 美國 | ✅ | ✅ | ✅ | ✅ | ❌ |
| A股 | ✅ | ✅ | ❌ | ❌ | ❌ |

## ⚙️ 配置說明

系統配置文件位於 `config/settings.json`，主要配置項：

```json
{
  "futu_api": {
    "host": "127.0.0.1",
    "port": 11111,
    "trade_password": "您的交易密碼"
  },
  "gui": {
    "window_title": "富途量化交易系統 v2.0",
    "window_size": "1400x900"
  },
  "trading": {
    "default_environment": "simulate",
    "confirm_real_trades": true
  }
}
```

## 📁 項目結構

```
futu-trading-system/
├── main.py                 # 主程序入口
├── requirements.txt        # 依賴包列表
├── README.md              # 項目說明
├── config/                # 配置文件
│   └── settings.json
├── core/                  # 核心模塊
│   ├── __init__.py
│   ├── config_manager.py  # 配置管理
│   ├── futu_client.py     # 富途API客戶端
│   └── data_manager.py    # 數據管理
├── gui/                   # GUI界面
│   ├── __init__.py
│   ├── main_window.py     # 主窗口
│   ├── trading_panel.py   # 交易面板
│   ├── kline_panel.py     # K線面板
│   └── monitoring_panel.py # 監控面板
└── data/                  # 數據存儲目錄
    ├── kline/            # K線數據
    ├── trades/           # 交易記錄
    └── logs/             # 日誌文件
```

## 🔧 開發說明

### 核心架構

- **模塊化設計**: 清晰的模塊分離，便於維護和擴展
- **配置驅動**: 基於配置文件的靈活配置系統
- **異步處理**: 使用線程處理耗時操作，保證界面響應
- **錯誤處理**: 完善的錯誤處理和日誌記錄機制

### 擴展開發

1. **添加新功能面板**
   - 在 `gui/` 目錄下創建新的面板類
   - 在 `main_window.py` 中註冊新面板

2. **添加新的數據源**
   - 擴展 `futu_client.py` 添加新的API調用
   - 在 `data_manager.py` 中添加相應的數據處理

3. **自定義配置**
   - 修改 `config/settings.json` 添加新配置項
   - 在 `config_manager.py` 中添加配置訪問方法

## ⚠️ 注意事項

1. **風險提示**
   - 本系統僅供學習和研究使用
   - 實盤交易存在風險，請謹慎操作
   - 建議先在模擬環境中充分測試

2. **數據權限**
   - 部分行情數據需要相應的權限
   - 請確保您的富途賬戶具有相應的行情權限

3. **網絡要求**
   - 需要穩定的網絡連接
   - 建議使用有線網絡以確保數據傳輸穩定

## 🆘 常見問題

### Q: 無法連接到富途API
A: 請檢查：
- FutuOpenD是否已啟動並登錄
- 端口11111是否被占用
- 防火牆設置是否正確

### Q: 交易功能無法使用
A: 請檢查：
- 是否已設置正確的交易密碼
- 賬戶是否具有交易權限
- 是否在交易時間內

### Q: K線圖表顯示異常
A: 請檢查：
- 是否安裝了matplotlib和mplfinance
- 股票代碼格式是否正確
- 是否有相應的行情權限

## 📞 技術支持

- 富途OpenAPI文檔: https://openapi.futunn.com
- 富途開發者社區: https://q.futunn.com
- 問題反饋: 請在GitHub Issues中提交

## 📄 許可證

本項目基於MIT許可證開源，詳見LICENSE文件。

---

**免責聲明**: 本軟件僅供教育和研究目的使用。使用本軟件進行實際交易的任何損失，開發者概不負責。請在充分了解風險的情況下使用。
